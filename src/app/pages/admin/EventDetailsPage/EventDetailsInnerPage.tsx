import { DownloadOutlined, ExclamationCircleOutlined } from '@ant-design/icons';
import { PToast, useToastManager } from '@porsche-design-system/components-react';
import { Button, Grid, Modal, message } from 'antd';
import { Formik, FormikHelpers } from 'formik';
import { useCallback, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import * as permissionKind from '../../../../shared/permissions';
import { EventDataFragment } from '../../../api/fragments/EventData';
import { useDeleteEventMutation } from '../../../api/mutations/deleteEvent';
import { useGetEventApplicationsQuery } from '../../../api/queries/getEventApplications';
import {
    ApplicationStage,
    ContentRefinementSourceAction,
    ContentRefinementSourceKind,
    LeadStageOption,
} from '../../../api/types';
import FormAutoTouch from '../../../components/FormAutoTouch';
import { useAccountContext } from '../../../components/contexts/AccountContextManager';
import { useMultipleDealerIds } from '../../../components/contexts/DealerContextManager';
import GlobalStateContextProvider from '../../../components/contexts/GenericStateOnPageContextManager';
import { ExportModalInput, useExportFormatModal } from '../../../components/fields/DownloadModal/ExportFormatModal';
import { usePasswordModal } from '../../../components/fields/DownloadModal/PasswordModal';
import Form from '../../../components/fields/Form';
import ConsolePageWithHeader from '../../../layouts/ConsoleLayout/ConsolePageWithHeader';
import { getApplicationIdentifier } from '../../../utilities/application';
import {
    streamExportEventsLead,
    streamExportEventsApplication,
} from '../../../utilities/export/streamEventsApplication';
import { ExportFormat } from '../../../utilities/export/types';
import getApolloErrors from '../../../utilities/getApolloErrors';
import hasPermissions from '../../../utilities/hasPermissions';
import EventDetailTabPanes from './EventDetailTabPanes';
import EventDetailsTabs from './EventDetailsTabs';
import { useEventFormValidator } from './EventForm/shared';
import Tab from './shared';
import { useEventInitialValues, useEventUpdateSubmission } from './utils';

export type EventDetailsInnerPageProps = {
    event: EventDataFragment;
    disabled?: boolean;
};

const EventDetailsInnerPage = ({ event, disabled = false }: EventDetailsInnerPageProps) => {
    const { t } = useTranslation(['eventDetails']);

    const [currentTab, setCurrentTab] = useState<Tab>(Tab.MainDetails);
    const [loadedReservationIds, setLoadedReservationIds] = useState<string[]>([]);
    const [loadedLeadIds, setLoadedLeadIds] = useState<string[]>([]);
    const [loadedAppointmentIds, setLoadedAppointmentIds] = useState<string[]>([]);
    const [selectedReservations, setSelectedReservations] = useState<string[]>([]);
    const [selectedLeads, setSelectedLeads] = useState<string[]>([]);
    const [selectedAppointments, setSelectedAppointments] = useState<string[]>([]);
    const [onDownloadLoading, setOnDownloadLoading] = useState(false);

    const [deleteEventMutation] = useDeleteEventMutation();
    const { data } = useGetEventApplicationsQuery({ variables: { eventId: event.id }, skip: !event.id });
    const screens = Grid.useBreakpoint();

    const passwordModal = usePasswordModal();

    const { id: eventId, identifier, displayName: eventDisplayName } = event;

    const { token } = useAccountContext();

    const navigate = useNavigate();

    const goBack = useCallback(() => {
        navigate('/admin/events');
    }, [navigate]);

    const { dealerIds } = useMultipleDealerIds();

    const refineContentParams = useMemo(
        () => ({
            hasPermissionToRefineContent: hasPermissions(event.permissions, [permissionKind.updateEvent]),
            source: {
                kind: ContentRefinementSourceKind.Event,
                action: ContentRefinementSourceAction.Update,
                identifier: eventId,
            },
        }),
        [event.permissions, eventId]
    );

    const exportLeadFormatModal = useExportFormatModal({
        initialFormat: { format: ExportFormat.CapFormat },
        dateRangeVisible: true,
        includeReportingFormat: true,
        preventCloseOnDownload: true,
    });

    const exportApplicationFormatModal = useExportFormatModal({
        dateRangeVisible: true,
        includeCapFormat: false,
        includeReportingFormat: true,
        preventCloseOnDownload: true,
    });

    const { addMessage } = useToastManager();

    const downloadHandler = useCallback(
        async ({ format, period }: ExportModalInput, actions: FormikHelpers<ExportModalInput>) => {
            setOnDownloadLoading(true);
            actions.setFieldError('period', undefined);

            const getExportInfoByTab = (tab: Tab) => {
                switch (tab) {
                    case Tab.Reservations:
                        return { stage: ApplicationStage.Reservation, applicationIds: selectedReservations };

                    case Tab.Leads:
                        return { leadIds: selectedLeads, period };

                    case Tab.Appointments:
                        return { stage: ApplicationStage.Appointment, applicationIds: selectedAppointments };

                    default:
                        throw new Error('Invalid tab for downloading applications');
                }
            };

            const baseExportValues = {
                eventId,
                dealerIds,
                identifier,
                token,
                eventDisplayName,
            };

            const { applicationIds, stage, leadIds } = getExportInfoByTab(currentTab);

            if (currentTab === Tab.Leads) {
                const response = await streamExportEventsLead({
                    ...baseExportValues,
                    leadIds,
                    stage: LeadStageOption.LeadAndContact,
                    format,
                    period,
                });

                setOnDownloadLoading(false);

                if (response.status === 200) {
                    exportLeadFormatModal.close();

                    addMessage({
                        text: t('eventDetails:download.initiated'),
                        state: 'success',
                    });
                } else {
                    actions.setFieldError(
                        'period',
                        response.status === 400
                            ? t('eventDetails:download.downloadNotCompleted')
                            : t('eventDetails:download.noRecordToDownload')
                    );
                }
            } else {
                const response = await streamExportEventsApplication({
                    ...baseExportValues,
                    applicationIds,
                    stage,
                    format,
                    period,
                });

                setOnDownloadLoading(false);

                if (response.status === 200) {
                    exportApplicationFormatModal.close();

                    addMessage({
                        text: t('eventDetails:download.initiated'),
                        state: 'success',
                    });
                } else {
                    actions.setFieldError(
                        'period',
                        response.status === 400
                            ? t('eventDetails:download.downloadNotCompleted')
                            : t('eventDetails:download.noRecordToDownload')
                    );
                }
            }
        },
        [
            currentTab,
            eventId,
            dealerIds,
            token,
            identifier,
            exportLeadFormatModal,
            exportApplicationFormatModal,
            selectedReservations,
            selectedLeads,
            selectedAppointments,
            passwordModal,
            addMessage,
            t,
        ]
    );

    const onDownloadLeadClick = useCallback(() => {
        exportLeadFormatModal.open();
    }, [exportLeadFormatModal]);

    const onDownloadApplicationClick = useCallback(() => {
        exportApplicationFormatModal.open();
    }, [exportApplicationFormatModal]);

    const relatedIdentifiers = useMemo(() => {
        if (!data?.applications?.length) {
            return null;
        }

        return data.applications
            .flatMap(application => [
                getApplicationIdentifier(application, ApplicationStage.Reservation),
                getApplicationIdentifier(application, ApplicationStage.Appointment),
            ])
            .filter((item, index, arr) => arr.indexOf(item) === index && !!item)
            .join(', ');
    }, [data]);

    const onDeleteEventClick = useCallback(async () => {
        Modal.confirm({
            className: 'static-modal',
            title: t('eventDetails:modals.deleteEvent.title'),
            icon: <ExclamationCircleOutlined />,
            content:
                relatedIdentifiers === null
                    ? t('eventDetails:modals.deleteEvent.contentEmpty')
                    : t('eventDetails:modals.deleteEvent.content', { relatedIdentifiers }),
            okText: t('eventDetails:modals.deleteEvent.okText'),
            okType: 'danger',
            cancelText: t('eventDetails:modals.deleteEvent.cancelText'),
            async onOk() {
                try {
                    // loading message
                    message.loading({
                        content: t('eventDetails:messages.deleteSubmitting'),
                        key: 'primary',
                        duration: 0,
                    });

                    await deleteEventMutation({ variables: { id: eventId } });

                    // show success
                    message.success({
                        content: t('eventDetails:messages.deleteSuccessful'),
                        key: 'primary',
                    });

                    goBack();
                } catch (error) {
                    const apolloErrors = getApolloErrors(error);

                    let errorMessage = t('eventDetails:messages.deleteFailed');
                    if (apolloErrors?.$root) {
                        errorMessage = apolloErrors?.$root;
                    } else if (apolloErrors?.id) {
                        errorMessage = apolloErrors?.id;
                    }

                    message.error({
                        content: errorMessage,
                        key: 'primary',
                    });
                }
            },
        });
    }, [deleteEventMutation, eventId, goBack, relatedIdentifiers, t]);

    const validate = useEventFormValidator(event.firstRouterPath);

    const onSubmit = useEventUpdateSubmission(event);

    const initialValues = useEventInitialValues(event);

    const hasDeletePermission = useMemo(() => hasPermissions(event.permissions, [permissionKind.deleteEvent]), [event]);

    const extra = useMemo(
        () => (
            <>
                {currentTab === Tab.Reservations && loadedReservationIds.length > 0 && (
                    <Button
                        icon={<DownloadOutlined />}
                        loading={onDownloadLoading}
                        onClick={onDownloadApplicationClick}
                        type="primary"
                    >
                        {t('eventDetails:actions.download')}
                    </Button>
                )}
                {currentTab === Tab.Leads && loadedLeadIds.length > 0 && (
                    <Button
                        icon={<DownloadOutlined />}
                        loading={onDownloadLoading}
                        onClick={onDownloadLeadClick}
                        type="primary"
                    >
                        {t('eventDetails:actions.download')}
                    </Button>
                )}
                {currentTab === Tab.Appointments && loadedAppointmentIds.length > 0 && (
                    <Button
                        icon={<DownloadOutlined />}
                        loading={onDownloadLoading}
                        onClick={onDownloadApplicationClick}
                        type="primary"
                    >
                        {t('eventDetails:actions.download')}
                    </Button>
                )}
                {currentTab === Tab.MainDetails && hasDeletePermission && (
                    <Button onClick={onDeleteEventClick} danger>
                        {t('eventDetails:actions.deleteEvent')}
                    </Button>
                )}
            </>
        ),
        [
            currentTab,
            loadedReservationIds.length,
            selectedReservations.length,
            onDownloadLoading,
            onDownloadLeadClick,
            onDownloadApplicationClick,
            t,
            loadedLeadIds.length,
            loadedAppointmentIds.length,
            hasDeletePermission,
            onDeleteEventClick,
        ]
    );

    const footer = useMemo(() => {
        switch (currentTab) {
            case Tab.MainDetails:
            case Tab.CustomerInformation:
                return [
                    !disabled && (
                        <Button key="update" form="eventForm" htmlType="submit" type="primary">
                            {t('eventDetails:actions.update')}
                        </Button>
                    ),
                ].filter(Boolean);

            default:
                return [];
        }
    }, [currentTab, disabled, t]);

    return (
        <GlobalStateContextProvider refineContent={refineContentParams}>
            {passwordModal.render()}
            <Formik initialValues={initialValues} onSubmit={onSubmit} validate={validate}>
                {({ handleSubmit }) => (
                    <Form id="eventForm" name="eventForm" onSubmitCapture={handleSubmit}>
                        <FormAutoTouch />

                        <ConsolePageWithHeader
                            extra={extra}
                            footer={footer}
                            header={{
                                footer: (
                                    <EventDetailsTabs
                                        activeTab={currentTab}
                                        event={event}
                                        pageType="Admin"
                                        setActiveTab={setCurrentTab}
                                    />
                                ),
                            }}
                            onBack={goBack}
                            title={
                                screens.md
                                    ? t('eventDetails:detailTitle', {
                                          name: initialValues.displayName,
                                          id: identifier,
                                      })
                                    : `${initialValues.displayName} ${identifier}`
                            }
                        >
                            <EventDetailTabPanes
                                activeTab={currentTab}
                                disabled={disabled}
                                event={event}
                                goBack={goBack}
                                pageType="Admin"
                                setLoadedAppointmentIds={setLoadedAppointmentIds}
                                setLoadedLeadIds={setLoadedLeadIds}
                                setLoadedReservationIds={setLoadedReservationIds}
                                setSelectedAppointments={setSelectedAppointments}
                                setSelectedLeads={setSelectedLeads}
                                setSelectedReservations={setSelectedReservations}
                            />
                        </ConsolePageWithHeader>
                    </Form>
                )}
            </Formik>

            {exportLeadFormatModal.render({
                title: t('eventDetails:modals.downloadApplication.title'),
                description: t('eventDetails:modals.downloadApplication.description'),
                onDownload: downloadHandler,
                forAdmin: true,
            })}
            {exportApplicationFormatModal.render({
                title: t('eventDetails:modals.downloadApplication.title'),
                description: t('eventDetails:modals.downloadApplication.description'),
                onDownload: downloadHandler,
                forAdmin: true,
            })}
            <PToast />
        </GlobalStateContextProvider>
    );
};

export default EventDetailsInnerPage;
