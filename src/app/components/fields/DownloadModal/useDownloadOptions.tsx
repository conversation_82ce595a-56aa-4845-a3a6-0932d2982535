import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { Timeframe } from '../../../api/types';
import { ExportFormat } from '../../../utilities/export';

type Params = {
    includeCapFormat?: boolean;
    includeReportingFormat?: boolean;
};

const useDownloadOptions = ({ includeCapFormat = true, includeReportingFormat = false }: Params = {}) => {
    const { t } = useTranslation(['ciModal']);

    return useMemo(() => {
        const timeframeOptions = [
            { label: t('ciModal:options.timeframe.currentMonth'), value: Timeframe.CurrentMonth },
            { label: t('ciModal:options.timeframe.lastMonth'), value: Timeframe.LastMonth },
            { label: t('ciModal:options.timeframe.last3Months'), value: Timeframe.Last3Months },
            { label: t('ciModal:options.timeframe.last6Months'), value: Timeframe.Last6Months },
            { label: t('ciModal:options.timeframe.last12Months'), value: Timeframe.Last12Months },
        ];

        const formatOptions = [
            { label: t('ciModal:options.format.defaultFormat'), value: ExportFormat.DefaultFormat },
            ...(includeCapFormat
                ? [{ label: t('ciModal:options.format.c@pFormat'), value: ExportFormat.CapFormat }]
                : []),
            ...(includeReportingFormat
                ? [{ label: t('ciModal:options.format.reportingFormat'), value: ExportFormat.ReportingFormat }]
                : []),
        ];

        return {
            timeframeOptions,
            formatOptions,
        };
    }, [includeReportingFormat, includeCapFormat, t]);
};

export default useDownloadOptions;
