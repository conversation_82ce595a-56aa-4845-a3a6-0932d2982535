import { Col, Form, Row, Typography } from 'antd';
import dayjs, { Dayjs } from 'dayjs';
import { Formik } from 'formik';
import type { RangePickerProps } from 'rc-picker';
import { useCallback, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import type { PeriodPayload } from '../../../api/types';
import { useThemeComponents } from '../../../themes/hooks';
import { ExportFormat } from '../../../utilities/export';
import { DownloadFormItem } from './ui';
import useDownloadOptions from './useDownloadOptions';

export type ExportModalInput = {
    format?: ExportFormat;
    period?: PeriodPayload;
};

const allowEmpty: [boolean, boolean] = [true, true];

type ExportFormatModalProps = {
    forAdmin?: boolean;
    onClose: () => void;
    onDownload: (value: ExportModalInput) => Promise<void>;
    visible: boolean;
    title: string;
    description?: string;
    initialValue?: ExportModalInput;
    dateRangeVisible?: boolean;
    includeCapFormat?: boolean;
    includeReportingFormat?: boolean;
};

const ExportFormatModal = ({
    forAdmin,
    visible,
    onClose,
    onDownload,
    title,
    initialValue,
    description,
    dateRangeVisible = false,
    includeCapFormat = true,
    includeReportingFormat = false,
}: ExportFormatModalProps) => {
    const { t } = useTranslation(['ciModal']);
    const [isDownloading, setIsDownloading] = useState(false);
    const { formatOptions } = useDownloadOptions({ includeCapFormat, includeReportingFormat });

    const {
        Modal,
        FormFields: { RangePickerField, SelectField },
    } = useThemeComponents();

    const submit = useCallback(
        async (values: ExportModalInput) => {
            setIsDownloading(true);

            await onDownload(values);
            setIsDownloading(false);

            onClose();
        },
        [onClose, onDownload]
    );

    const disabledDate: RangePickerProps<Dayjs>['disabledDate'] = useCallback(value => {
        const invalidDate = dayjs().add(1, 'day');

        return dayjs(value).isSameOrAfter(invalidDate, 'day');
    }, []);

    return (
        <Formik initialValues={initialValue ?? {}} onSubmit={submit}>
            {({ values, submitForm }) => {
                const isDateRangeValid = !dateRangeVisible || (values.period?.start && values.period?.end);

                return (
                    <Modal
                        cancelButtonProps={{ disabled: isDownloading }}
                        cancelText={t('ciModal:actions.cancel')}
                        okButtonProps={{
                            loading: isDownloading,
                            disabled: isDownloading || !isDateRangeValid,
                        }}
                        okText={t('ciModal:actions.download')}
                        onCancel={onClose}
                        onOk={submitForm}
                        open={visible}
                        title={title}
                        width={480}
                        centered
                    >
                        <Form id="exportModalForm" name="exportModalForm">
                            <Row gutter={[14, 14]}>
                                {description && (
                                    <Col span={24}>
                                        <Typography>{description}</Typography>
                                    </Col>
                                )}
                                <Col span={24}>
                                    <DownloadFormItem $forAdmin={forAdmin}>
                                        <SelectField
                                            bordered={forAdmin}
                                            name="format"
                                            options={formatOptions}
                                            placeholder={t('ciModal:options.placeHolders.selectFormat')}
                                            showSearch={forAdmin}
                                        />
                                    </DownloadFormItem>
                                </Col>
                                {dateRangeVisible && (
                                    <Col span={24}>
                                        <DownloadFormItem $forAdmin={forAdmin}>
                                            <RangePickerField
                                                allowEmpty={allowEmpty}
                                                bordered={forAdmin}
                                                defaultValue={undefined}
                                                disabledDate={disabledDate}
                                                label=""
                                                name="period"
                                                picker="date"
                                                showTime={false}
                                            />
                                        </DownloadFormItem>
                                    </Col>
                                )}
                            </Row>
                        </Form>
                    </Modal>
                );
            }}
        </Formik>
    );
};

type UseExportFormatModalProps = {
    initialFormat?: ExportModalInput;
    dateRangeVisible?: boolean;
    includeCapFormat?: boolean;
    includeReportingFormat?: boolean;
};

export const useExportFormatModal = (props: UseExportFormatModalProps = {}) => {
    const [visible, setVisible] = useState(false);

    const actions = useMemo(
        () => ({
            open: () => setVisible(true),
            close: () => setVisible(false),
        }),
        []
    );

    return {
        open: actions.open,
        close: actions.close,
        render: ({
            title,
            description,
            forAdmin,
            onDownload,
        }: Pick<ExportFormatModalProps, 'title' | 'description' | 'forAdmin' | 'onDownload'>) => (
            <ExportFormatModal
                description={description}
                forAdmin={forAdmin}
                onClose={actions.close}
                onDownload={onDownload}
                title={title}
                visible={visible}
                {...props}
            />
        ),
    };
};

export default ExportFormatModal;
