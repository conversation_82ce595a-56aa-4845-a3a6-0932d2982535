import type { <PERSON><PERSON><PERSON><PERSON><PERSON> } from 'express';
import { isArray, isString } from 'lodash/fp';
import { ObjectId } from 'mongodb';

import { ApplicationStage as Stage } from '../../app/utilities/getApplicationFileName';
import type { RequestLocals } from '../core/express';
import { ApplicationKind, ApplicationStatus } from '../database';
import type { EventApplicationModule } from '../database';
import getDatabaseContext from '../database/getDatabaseContext';
import { mainQueue } from '../queues';
import { getSessionDataFromRequest } from '../schema/session';
import { ExcelExportFormat } from '../utils/excel/enums';
import { uniqueObjectIds } from '../utils/fp';
import { getBEApplicationStage } from './exportApplications';
import type { PeriodPayload } from './type';
import { getPeriodFilter } from './utils';

interface Param {
    eventId: string;
    [key: string]: string;
}

interface ExportRequestBody {
    dealerIds: string[];
    applicationIds: string[];
    stage: Stage;
    period?: PeriodPayload;
    format: ExcelExportFormat.system | ExcelExportFormat.reporting;
    languageId?: string;
    nonce?: string;
    filename?: string[];
    company?: { countryCode?: string; displayName?: string };
}

type QueryParam = Record<string, never>;

/**
 * Queue a background job to export event applications and send the result via email
 */
const streamExportEventApplications: RequestHandler<
    Param,
    unknown,
    ExportRequestBody,
    QueryParam,
    RequestLocals
> = async (req, res, next) => {
    try {
        const { eventId: inputEventId } = req.params;

        if (!ObjectId.isValid(inputEventId)) {
            res.status(400).send('Bad request');

            return;
        }

        const {
            dealerIds: queryDealerIds,
            applicationIds: applicationStringIds,
            stage: inputStage,
            period,
            format,
            languageId,
            filename,
        } = req.body;

        // Validate application ids
        if ((!isArray(applicationStringIds) || !applicationStringIds?.length) && !period) {
            res.status(400).send('Bad request');

            return;
        }

        const applicationIds = uniqueObjectIds(
            (applicationStringIds ?? []).filter(id => isString(id) && ObjectId.isValid(id)).map(it => new ObjectId(it))
        );

        // If it's still empty, return 404
        if (applicationIds.length === 0 && !period) {
            res.status(404).send('Not found');

            return;
        }

        const stage = getBEApplicationStage(inputStage);
        if (!stage) {
            res.status(400).send('Bad request');

            return;
        }

        const { collections } = await getDatabaseContext();

        const eventId = new ObjectId(inputEventId);
        const event = await collections.events.findOne({ _id: eventId });
        if (!event) {
            res.status(404).send('Not found');

            return;
        }

        const userToken = req.headers.Authorization as string;
        const session = await getSessionDataFromRequest(req, userToken);
        const { userId } = session;

        // Get dealer IDs from request
        const dealerIdsInRequest = queryDealerIds?.map(dealerId => new ObjectId(dealerId)) ?? [];

        // Get event module
        const eventModule = (await collections.modules.findOne({
            _id: event.moduleId,
        })) as EventApplicationModule;

        if (!eventModule) {
            res.status(404).send('Event module not found');

            return;
        }

        const periodFilter = period
            ? getPeriodFilter(period.start ? new Date(period.start) : null, period.end ? new Date(period.end) : null)
            : {};

        const totalApplications = await collections.applications.countDocuments({
            eventId,
            isDraft: false,
            status: { $ne: ApplicationStatus.Drafted },
            dealerId: { $in: dealerIdsInRequest },
            kind: ApplicationKind.Event,
            '_versioning.isLatest': true,
            ...periodFilter,
        });

        if (totalApplications === 0) {
            res.status(204).end();

            return;
        }

        await mainQueue.add({
            type: 'processEventApplicationExport',
            userId,
            eventId: inputEventId,
            applicationIds: applicationStringIds,
            dealerIds: queryDealerIds,
            stage,
            period,
            format,
            nonce: req.body.nonce,
            languageId,
            filename,
        });

        res.status(200).send('OK');
    } catch (error) {
        console.error('Error in streamExportEventApplications:', error);

        if (!res.headersSent) {
            res.status(500).send(
                `Error queuing export job: ${error instanceof Error ? error.message : 'Unknown error'}`
            );
        } else {
            next(error);
        }
    }
};

export default streamExportEventApplications;
