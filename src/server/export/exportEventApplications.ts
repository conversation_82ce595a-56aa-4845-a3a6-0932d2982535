import { <PERSON>quest<PERSON><PERSON><PERSON> } from 'express';
import { isArray, isString } from 'lodash/fp';
import { ObjectId } from 'mongodb';
import XlsxPopulate from 'xlsx-populate';
import getApplicationFileName, { ApplicationStage as Stage } from '../../app/utilities/getApplicationFileName';
import { RequestLocals } from '../core/express';
import {
    ApplicationKind,
    ApplicationResubmittedToSystemAuditTrail,
    ApplicationStatus,
    ApplicationSubmittedToSystemAuditTrail,
    AuditTrailKind,
    EventApplication,
    EventApplicationModule,
    PasswordConfiguration,
} from '../database';
import getDatabaseContext from '../database/getDatabaseContext';
import { ApplicationPolicyAction } from '../permissions';
import { mainQueue } from '../queues';
import { getSessionDataFromRequest } from '../schema/session';
import getExcelApplicationRows from '../utils/excel/applications';
import { ExcelExportFormat } from '../utils/excel/enums';
import { uniqueObjectIds } from '../utils/fp';
import { getBEApplicationStage } from './exportApplications';
import { getDealerIdsFromRequest, getPassword } from './utils';

type Param = {
    eventId: string;
};

type QueryParam = {};

type ExportRequestBody = {
    nonce: string;
    applicationIds: string[];
    leadIds: string[];
    kind: string;
    dealerIds: string[];
    stage: Stage;
    languageId?: string;
};

const exportEventApplications: RequestHandler<Param, unknown, ExportRequestBody, QueryParam, RequestLocals> = async (
    req,
    res,
    next
) => {
    const { getPermissionController, loaders } = res.locals.context;
    const permissions = await getPermissionController();

    const { eventId: inputEventId } = req.params;

    if (!ObjectId.isValid(inputEventId)) {
        res.status(400).send('Bad request');

        return;
    }

    const { dealerIds: queryDealerIds, applicationIds: applicationStringIds, stage: inputStage, languageId } = req.body;

    // Validate reservation and lead ids
    if (!isArray(applicationStringIds) || !applicationStringIds?.length) {
        res.status(400).send('Bad request');

        return;
    }

    const applicationIds = uniqueObjectIds(
        (applicationStringIds ?? []).filter(id => isString(id) && ObjectId.isValid(id)).map(it => new ObjectId(it))
    );

    // If it's still empty, return 404
    if (applicationIds.length === 0) {
        res.status(404).send('Not found');

        return;
    }

    const stage = getBEApplicationStage(inputStage);
    if (!stage) {
        res.status(400).send('Bad request');

        return;
    }

    const { collections } = await getDatabaseContext();

    const eventId = new ObjectId(inputEventId);
    const event = await collections.events.findOne({ _id: eventId });
    if (!event) {
        res.status(404).send('Not found');

        return;
    }

    const eventModule = (await collections.modules.findOne({ _id: event.moduleId })) as EventApplicationModule;

    // get user from token
    const userToken = req.headers.Authorization as string;
    const session = await getSessionDataFromRequest(req, userToken);
    const { userId } = session;
    const user = await collections.users.findOne({ _id: userId });

    const company = await collections.companies.findOne({ _id: eventModule.companyId });
    if (!company) {
        res.status(404).send('Not found');

        return;
    }

    const dealerIdsInRequest = getDealerIdsFromRequest(queryDealerIds);
    const applicationPermission = await permissions.applications.getFilterQueryForAction(ApplicationPolicyAction.View, [
        stage,
    ]);

    const getApplicationsBySystemFormat = async () => {
        // For format default, we need to get the version suite id instead
        // and filter it by submitted audit trail
        const applicationSuiteIds = await collections.applications
            .find({
                _id: { $in: applicationIds },
                eventId,
                isDraft: false,
                status: { $ne: ApplicationStatus.Drafted },
                dealerId: { $in: dealerIdsInRequest },
                kind: ApplicationKind.Event,
                '_versioning.isLatest': true,
            })
            .map(item => item._versioning.suiteId)
            .toArray();

        // Get submitted audit trails
        const submittedAuditTrailApplicationIds = await collections.auditTrails
            .find({
                _kind: {
                    // For internal would be submission to system
                    $in: [AuditTrailKind.ApplicationSubmittedToSystem, AuditTrailKind.ApplicationResubmittedToSystem],
                },
                applicationSuiteId: { $in: applicationSuiteIds },
            })
            .map(
                (auditTrail: ApplicationSubmittedToSystemAuditTrail | ApplicationResubmittedToSystemAuditTrail) =>
                    auditTrail.applicationId
            )
            .toArray();

        // Get final result
        const results = await collections.applications
            .find({
                $and: [
                    applicationPermission,
                    {
                        eventId,
                        isDraft: false,
                        status: { $ne: ApplicationStatus.Drafted },
                        dealerId: { $in: dealerIdsInRequest },
                        kind: ApplicationKind.Event,

                        $or: [{ _id: { $in: applicationIds } }, { _id: { $in: submittedAuditTrailApplicationIds } }],
                    },
                ],
            })
            .toArray();

        return results as EventApplication[];
    };

    const applications: EventApplication[] = await getApplicationsBySystemFormat();

    // Check applications again if it's empty
    if (!applications.length) {
        res.status(404).send('Not found');

        return;
    }

    try {
        const { nonce } = req.body;

        const passwordProtect = company.passwordConfiguration !== PasswordConfiguration.Off;
        const password = passwordProtect ? await getPassword(nonce) : null;

        const rows = await getExcelApplicationRows(
            applications,
            [eventModule],
            {
                format: ExcelExportFormat.system,
                stage,
                currencyCode: company.currency,
                timeZone: company.timeZone,
                routerFirstLanguage: languageId || null,
            },
            loaders
        );

        const workbook = await XlsxPopulate.fromBlankAsync();
        const defaultWorksheet = workbook.sheet(0).name('Default Worksheet');
        defaultWorksheet.cell('A1').value(rows);

        const filename = encodeURI(getApplicationFileName(company.legalName.defaultValue, undefined, inputStage));

        res.set({
            ...(passwordProtect && { 'X-EXCEL-PASSWORD': password }),
            'Content-Disposition': `attachment; filename="${filename}.xlsx"`,
            'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        });

        const buffer = await workbook.outputAsync({ ...(passwordProtect && { password }) });

        const date = new Date();

        if (passwordProtect) {
            // send email to user
            await mainQueue.add({
                type: 'sendExportApplicationPasswordEmail',
                password,
                date,
                company,
                applicationType: 'Events',
                documentType: 'Excel',
                user,
            });
        }

        res.send(buffer);
    } catch (error) {
        next(error);
    }
};

export default exportEventApplications;
