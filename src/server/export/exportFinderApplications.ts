import { Request<PERSON><PERSON><PERSON> } from 'express';
import { isArray, isString } from 'lodash/fp';
import { ObjectId } from 'mongodb';
import XlsxPopulate from 'xlsx-populate';
import getApplicationFileName, { ApplicationStage as Stage } from '../../app/utilities/getApplicationFileName';
import { RequestLocals } from '../core/express';
import {
    ApplicationKind,
    ApplicationResubmittedToSystemAuditTrail,
    ApplicationStage,
    ApplicationStatus,
    ApplicationSubmittedToSystemAuditTrail,
    AuditTrailKind,
    FinderApplication,
    FinderApplicationModule,
    PasswordConfiguration,
} from '../database';
import getDatabaseContext from '../database/getDatabaseContext';
import { ApplicationPolicyAction } from '../permissions';
import { mainQueue } from '../queues';
import { getSessionDataFromRequest } from '../schema/session';
import getExcelApplicationRows from '../utils/excel/applications';
import { ExcelExportFormat } from '../utils/excel/enums';
import { uniqueObjectIds } from '../utils/fp';
import { getBEApplicationStage } from './exportApplications';
import { getDealerIdsFromRequest, getPassword } from './utils';

type Param = {
    configuratorId: string;
};

type QueryParam = {};

type ExportRequestBody = {
    nonce: string;
    applicationIds: string | string[];
    dealerIds: string[];
    stage: Stage;
    languageId?: string;
};

export const exportFinderApplications: RequestHandler<
    Param,
    unknown,
    ExportRequestBody,
    QueryParam,
    RequestLocals
> = async (req, res, next) => {
    const { getPermissionController, loaders } = res.locals.context;
    const permissions = await getPermissionController();

    const { stage: inputStage, dealerIds, applicationIds: applicationStringIds, languageId } = req.body;

    const stage: ApplicationStage = getBEApplicationStage(inputStage);
    if (!stage) {
        res.status(400).send('Bad request');

        return;
    }

    if (!isArray(applicationStringIds) || !applicationStringIds?.length) {
        res.status(400).send('Bad request');

        return;
    }

    // Validate application inputs again
    const applicationIds = uniqueObjectIds(
        applicationStringIds.filter(id => isString(id) && ObjectId.isValid(id)).map(it => new ObjectId(it))
    );
    if (!applicationIds.length) {
        res.status(404).send('Not found');

        return;
    }

    const { collections } = await getDatabaseContext();

    // get user from token
    const userToken = req.headers.Authorization as string;
    const session = await getSessionDataFromRequest(req, userToken);
    const { userId } = session;
    const user = await collections.users.findOne({ _id: userId });

    const dealerIdsInRequest = getDealerIdsFromRequest(dealerIds);
    const applicationPermission = await permissions.applications.getFilterQueryForAction(ApplicationPolicyAction.View, [
        stage,
    ]);

    const getApplicationsBySystemFormat = async () => {
        // For format default, we need to get the version suite id instead
        // and filter it by submitted audit trail
        const applicationSuiteIds = await collections.applications
            .find({
                _id: { $in: applicationIds },
                isDraft: false,
                status: { $ne: ApplicationStatus.Drafted },
                dealerId: { $in: dealerIdsInRequest },
                kind: ApplicationKind.Finder,
                '_versioning.isLatest': true,
            })
            .map(item => item._versioning.suiteId)
            .toArray();

        // Get submitted audit trails
        const submittedAuditTrailApplicationIds = await collections.auditTrails
            .find({
                _kind: {
                    // For internal would be submission to system
                    $in: [AuditTrailKind.ApplicationSubmittedToSystem, AuditTrailKind.ApplicationResubmittedToSystem],
                },
                applicationSuiteId: { $in: applicationSuiteIds },
            })
            .map(
                (auditTrail: ApplicationSubmittedToSystemAuditTrail | ApplicationResubmittedToSystemAuditTrail) =>
                    auditTrail.applicationId
            )
            .toArray();

        // Get final result
        const results = await collections.applications
            .aggregate<FinderApplication>([
                {
                    $match: {
                        $and: [
                            applicationPermission,
                            {
                                // From application filter
                                isDraft: false,
                                status: { $ne: ApplicationStatus.Drafted },
                                dealerId: { $in: dealerIdsInRequest },
                                kind: ApplicationKind.Finder,
                                $or: [
                                    { _id: { $in: applicationIds } },
                                    { _id: { $in: submittedAuditTrailApplicationIds } },
                                ],
                            },
                        ],
                    },
                },
            ])
            .toArray();

        return results;
    };

    const applications = await getApplicationsBySystemFormat();

    if (!applications.length) {
        res.status(400).send('Bad request');

        return;
    }
    const modules = (await collections.modules
        .find({ _id: { $in: applications.map(application => application.moduleId) } })
        .toArray()) as FinderApplicationModule[];
    if (!modules || !modules.length) {
        res.status(400).send('Bad request');
    }

    const company = await collections.companies.findOne({ _id: modules[0].companyId });
    if (!company) {
        res.status(400).send('Bad request');
    }

    try {
        const { nonce } = req.body;
        const passwordProtect = company.passwordConfiguration !== PasswordConfiguration.Off;
        const password = passwordProtect ? await getPassword(nonce) : null;

        const rows = await getExcelApplicationRows(
            applications,
            modules,
            {
                format: ExcelExportFormat.system,
                stage,
                currencyCode: company.currency,
                timeZone: company.timeZone,
                routerFirstLanguage: languageId || null,
            },
            loaders
        );

        const workbook = await XlsxPopulate.fromBlankAsync();
        const defaultWorksheet = workbook.sheet(0).name('Default Worksheet');
        defaultWorksheet.cell('A1').value(rows);

        const filename = encodeURI(getApplicationFileName(company.legalName.defaultValue, undefined, inputStage));

        res.set({
            ...(passwordProtect && { 'X-EXCEL-PASSWORD': password }),
            'Content-Disposition': `attachment; filename="${filename}.xlsx"`,
            'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        });

        const buffer = await workbook.outputAsync({ ...(passwordProtect && { password }) });

        const date = new Date();

        // send email to user
        if (passwordProtect) {
            await mainQueue.add({
                type: 'sendExportApplicationPasswordEmail',
                password,
                date,
                company,
                applicationType: 'Configurator',
                documentType: 'Excel',
                user,
            });
        }

        res.send(buffer);
    } catch (error) {
        next(error);
    }
};

export default exportFinderApplications;
