import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from 'express';
import { isArray, isString } from 'lodash/fp';
import { ObjectId } from 'mongodb';
import XlsxPopulate from 'xlsx-populate';
import getApplicationFileName, { ApplicationStage as Stage } from '../../app/utilities/getApplicationFileName';
import { RequestLocals } from '../core/express';
import {
    ApplicationKind,
    ApplicationResubmittedToSystemAuditTrail,
    ApplicationStage,
    ApplicationStatus,
    ApplicationSubmittedToSystemAuditTrail,
    AuditTrailKind,
    ConfiguratorApplication,
    ConfiguratorModule,
    PasswordConfiguration,
} from '../database';
import getDatabaseContext from '../database/getDatabaseContext';
import { ApplicationPolicyAction } from '../permissions';
import { mainQueue } from '../queues';
import { lookUpConfiguratorPipelines } from '../schema/resolvers/queries/applications/listApplication';
import { getSessionDataFromRequest } from '../schema/session';
import getExcelApplicationRows from '../utils/excel/applications';
import { ExcelExportFormat } from '../utils/excel/enums';
import { uniqueObjectIds } from '../utils/fp';
import { getBEApplicationStage } from './exportApplications';
import { getDealerIdsFromRequest, getPassword } from './utils';

type Param = {
    configuratorId: string;
};

type QueryParam = {};

type ExportRequestBody = {
    nonce: string;
    applicationIds: string | string[];
    dealerIds: string[];
    stage: Stage;
    languageId?: string;
};

export const exportConfiguratorApplications: RequestHandler<
    Param,
    unknown,
    ExportRequestBody,
    QueryParam,
    RequestLocals
> = async (req, res, next) => {
    const { getPermissionController, loaders } = res.locals.context;
    const permissions = await getPermissionController();

    const { configuratorId: inputConfiguratorId } = req.params;

    if (!ObjectId.isValid(inputConfiguratorId)) {
        res.status(400).send('Bad request');

        return;
    }

    const { stage: inputStage, dealerIds, applicationIds: applicationStringIds, languageId } = req.body;

    const stage: ApplicationStage = getBEApplicationStage(inputStage);
    if (!stage) {
        res.status(400).send('Bad request');

        return;
    }

    if (!isArray(applicationStringIds) || !applicationStringIds?.length) {
        res.status(400).send('Bad request');

        return;
    }

    // Validate application inputs again
    const applicationIds = uniqueObjectIds(
        applicationStringIds.filter(id => isString(id) && ObjectId.isValid(id)).map(it => new ObjectId(it))
    );
    if (!applicationIds.length) {
        res.status(404).send('Not found');

        return;
    }

    const { collections } = await getDatabaseContext();

    const configuratorId = new ObjectId(inputConfiguratorId);
    const configurator = await collections.configurators.findOne({ _id: configuratorId });
    if (!configurator) {
        res.status(404).send('Not found');

        return;
    }

    const configuratorModule = (await collections.modules.findOne({
        _id: configurator.moduleId,
    })) as ConfiguratorModule;

    // get user from token
    const userToken = req.headers.Authorization as string;
    const session = await getSessionDataFromRequest(req, userToken);
    const { userId } = session;
    const user = await collections.users.findOne({ _id: userId });

    const company = await collections.companies.findOne({ _id: configuratorModule.companyId });
    if (!company) {
        res.status(404).send('Not found');

        return;
    }

    const dealerIdsInRequest = getDealerIdsFromRequest(dealerIds);
    const applicationPermission = await permissions.applications.getFilterQueryForAction(ApplicationPolicyAction.View, [
        stage,
    ]);

    const getApplicationsBySystemFormat = async () => {
        // For format default, we need to get the version suite id instead
        // and filter it by submitted audit trail
        const applicationSuiteIds = await collections.applications
            .find({
                _id: { $in: applicationIds },
                isDraft: false,
                status: { $ne: ApplicationStatus.Drafted },
                dealerId: { $in: dealerIdsInRequest },
                kind: ApplicationKind.Configurator,
                '_versioning.isLatest': true,
            })
            .map(item => item._versioning.suiteId)
            .toArray();

        // Get submitted audit trails
        const submittedAuditTrailApplicationIds = await collections.auditTrails
            .find({
                _kind: {
                    // For internal would be submission to system
                    $in: [AuditTrailKind.ApplicationSubmittedToSystem, AuditTrailKind.ApplicationResubmittedToSystem],
                },
                applicationSuiteId: { $in: applicationSuiteIds },
            })
            .map(
                (auditTrail: ApplicationSubmittedToSystemAuditTrail | ApplicationResubmittedToSystemAuditTrail) =>
                    auditTrail.applicationId
            )
            .toArray();

        // Get final result
        const results = await collections.applications
            .aggregate<ConfiguratorApplication>([
                ...lookUpConfiguratorPipelines,
                {
                    $match: {
                        $and: [
                            applicationPermission,
                            {
                                // From application filter
                                isDraft: false,
                                status: { $ne: ApplicationStatus.Drafted },
                                dealerId: { $in: dealerIdsInRequest },
                                kind: ApplicationKind.Configurator,

                                // From lookup
                                'configurator.modelConfiguratorId': configuratorId,

                                $or: [
                                    { _id: { $in: applicationIds } },
                                    { _id: { $in: submittedAuditTrailApplicationIds } },
                                ],
                            },
                        ],
                    },
                },
            ])
            .toArray();

        return results;
    };

    const applications = await getApplicationsBySystemFormat();

    if (!applications.length) {
        res.status(400).send('Bad request');

        return;
    }

    try {
        const { nonce } = req.body;
        const passwordProtect = company.passwordConfiguration !== PasswordConfiguration.Off;
        const password = passwordProtect ? await getPassword(nonce) : null;

        const rows = await getExcelApplicationRows(
            applications,
            [configuratorModule],
            {
                format: ExcelExportFormat.system,
                stage,
                currencyCode: company.currency,
                routerFirstLanguage: languageId || null,
            },
            loaders
        );

        const workbook = await XlsxPopulate.fromBlankAsync();
        const defaultWorksheet = workbook.sheet(0).name('Default Worksheet');
        defaultWorksheet.cell('A1').value(rows);

        const filename = encodeURI(getApplicationFileName(company.legalName.defaultValue, undefined, inputStage));

        res.set({
            ...(passwordProtect && { 'X-EXCEL-PASSWORD': password }),
            'Content-Disposition': `attachment; filename="${filename}.xlsx"`,
            'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        });

        const buffer = await workbook.outputAsync({ ...(passwordProtect && { password }) });

        const date = new Date();

        // send email to user
        if (passwordProtect) {
            await mainQueue.add({
                type: 'sendExportApplicationPasswordEmail',
                password,
                date,
                company,
                applicationType: 'Configurator',
                documentType: 'Excel',
                user,
            });
        }

        res.send(buffer);
    } catch (error) {
        next(error);
    }
};

export default exportConfiguratorApplications;
