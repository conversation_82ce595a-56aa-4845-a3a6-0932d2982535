import type { TFunction } from 'i18next';
import { capitalize, flow, isNil, map, uniq, uniqBy } from 'lodash/fp';
import {
    ApplicationKind,
    ApplicationScenario,
    ApplicationStage,
    ModuleType,
    type EventApplication,
    type MobilityModule,
    type Module,
} from '../../../../database';
import { getTimeZoneOffset } from '../../../date';
import getSystemAppointmentSetting from '../shared/setting/appointment';
import { getCampaignValuesColumns } from '../shared/setting/campaign';
import { getCapColumns } from '../shared/setting/cap';
import type {
    PreparedSystemApplicationData,
    SystemKycSupportedApplicationModule,
    SystemSupportedApplicationModule,
} from '../shared/types';
import getSystemConsentSetting from './setting/consent';
import getSystemFinancingSetting from './setting/financing';
import getSystemInsuranceSetting from './setting/insurance';
import getSystemKycSetting from './setting/kyc';
import getSystemMainSetting, { type SystemMainColumnSetting } from './setting/main';
import getSystemMainSettingId from './setting/mainId';
import getSystemMobilitySetting from './setting/mobility';
import getSystemPaymentSetting from './setting/payment';
import getSystemVehicleSetting from './setting/vehicle';

type SupportedData = {
    t: TFunction;
    currencyCode?: string;
    timeZone?: string;
    stage?: ApplicationStage;
    hasAppointment: boolean;
    hasAffinBankAutoFinanceCentre?: boolean;
    language?: string;
};

const applicationModules = [
    ModuleType.StandardApplicationModule,
    ModuleType.FinderApplicationPublicModule,
    ModuleType.FinderApplicationPrivateModule,
    ModuleType.ConfiguratorModule,
    ModuleType.EventApplicationModule,
    ModuleType.GiftVoucherModule,
];

const getRowSettingForMobility = async (
    modules: MobilityModule[],
    { t, hasAppointment, timeZone, stage }: SupportedData
) => {
    const [mainColumn] = getSystemMainSetting(ApplicationStage.Mobility, {
        createdAt: timeZone
            ? t('applicationExcel:main.mobilityCreatedDateWithTimeZone', { timeZone: getTimeZoneOffset(t, timeZone) })
            : t('applicationExcel:main.mobilityCreatedDate'),
        lastActivity: timeZone
            ? t('applicationExcel:main.mobilityUpdatedAtWithTimeZone', {
                  timeZone: getTimeZoneOffset(t, timeZone),
              })
            : t('applicationExcel:main.mobilityUpdatedAt'),
        status: t('applicationExcel:main.status'),
        timeZone,
        t,
    });
    const [mobilityColumn] = getSystemMobilitySetting({ t, timeZone });
    const [vehicleColumn] = getSystemVehicleSetting(hasAppointment, t);
    const showMobilityDiscountDetails = true;
    const settings = (await Promise.all(modules.map(module => getSystemKycSetting(module, t)))).filter(Boolean);

    const [, allPaymentColumns] = getSystemPaymentSetting(
        ApplicationStage.Mobility,
        { t, timeZone },
        showMobilityDiscountDetails
    );

    const [, allConsentColumns] = await getSystemConsentSetting(
        t,
        modules.map(module => module.agreementsModuleId)
    );

    return [
        // Mobility Main details
        ...getMainSettingColumnId(stage),
        mainColumn.createdAt,
        mobilityColumn.startDate,
        mobilityColumn.endDate,
        vehicleColumn.variantName,
        vehicleColumn.variantId,
        mobilityColumn.location,
        mainColumn.status,
        mainColumn.assigneeName,
        // Mobility Activity
        mainColumn.lastActivity,
        mainColumn.dateOfSubmission,

        // Mobility Applicant details
        ...uniqBy(
            'key',
            settings.flatMap(([, allKycColumns]) => allKycColumns)
        ),

        // Mobility payment details
        ...allPaymentColumns,

        // Mobility Consents
        ...allConsentColumns,
    ];
};

const getMainSettingColumnId = (stage: ApplicationStage, moduleTypes?: ModuleType[]) => {
    const [mainColumnId, allMainColumnIds] = getSystemMainSettingId({
        id: `${capitalize(stage)} ID`,
    });

    const columns: SystemMainColumnSetting[] = [];

    // build column for ARC Applicaiton
    if (isNil(moduleTypes) || moduleTypes.some(moduleType => applicationModules.includes(moduleType))) {
        columns.push(...allMainColumnIds);
    }

    // return unique
    return uniqBy('key', columns);
};

const getMainSettingColumns = (moduleTypes: ModuleType[], support: SupportedData) => {
    const { stage, timeZone, t } = support;
    const [mainColumn, allMainColumns] = getSystemMainSetting(stage, {
        createdAt: timeZone ? `Date Created ${getTimeZoneOffset(t, timeZone)}` : 'Date Created',
        lastActivity: timeZone ? `Last Activity ${getTimeZoneOffset(t, timeZone)}` : 'Last Activity',
        status: 'Status',
        timeZone,
        t,
    });
    const columns: SystemMainColumnSetting[] = [];

    // build column for ARC Applicaiton
    if (moduleTypes.some(moduleType => applicationModules.includes(moduleType))) {
        columns.push(...allMainColumns);
    }

    // return unique
    return uniqBy('key', columns);
};

export const getKYCColumns = async (modules: SystemKycSupportedApplicationModule[], t: TFunction) => {
    const columns = [];

    // build column for ARC Applicaiton
    if (modules.some(module => applicationModules.includes(module._type))) {
        const settings = (await Promise.all(modules.map(module => getSystemKycSetting(module, t)))).filter(Boolean);

        columns.push(
            ...settings.flatMap(([, allKycColumns]) =>
                modules.length > 1
                    ? allKycColumns.filter(column =>
                          settings.every(setting => setting.find(value => value[column.header]))
                      )
                    : allKycColumns
            )
        );
    }

    // return unique
    return uniqBy('key', columns);
};

const getVehicleColumns = (moduleTypes: ModuleType[], hasTestDriveColumn: boolean, t: TFunction) => {
    const [vehicleColumn, allVehicleColumns] = getSystemVehicleSetting(hasTestDriveColumn, t);

    const columns = [];

    if (moduleTypes.some(moduleType => applicationModules.includes(moduleType))) {
        columns.push(...allVehicleColumns);
    }

    // return unique
    return uniqBy('key', columns);
};

const getFinancingColumns = (
    moduleTypes: ModuleType[],
    { t, currencyCode, hasAffinBankAutoFinanceCentre }: SupportedData
) => {
    const [financingColumn, allFinancingColumns] = getSystemFinancingSetting(
        t,
        currencyCode,
        hasAffinBankAutoFinanceCentre
    );
    const columns = [];

    if (moduleTypes.some(moduleType => applicationModules.includes(moduleType))) {
        columns.push(...allFinancingColumns);
    }

    // return unique
    return uniqBy('key', columns);
};

const getCustomizedFieldsColumns = (items: PreparedSystemApplicationData[]) => {
    const eventApplications = items.filter(({ kind }) => kind === ApplicationKind.Event) as EventApplication[];

    // DEBUG: Log customized fields analysis
    console.debug('[Excel] Customized fields analysis:', {
        totalItems: items.length,
        eventApplicationsCount: eventApplications.length,
        itemKinds: items.map(item => item.kind),
        hasCustomizedFields: eventApplications.length > 0,
    });

    if (!eventApplications.length) {
        console.debug('[Excel] No event applications found, returning empty customized fields columns');

        return [];
    }

    const customizedFields = eventApplications.flatMap(({ customizedFields }) => customizedFields);

    // DEBUG: Log customized fields details
    console.debug('[Excel] Found customized fields:', {
        customizedFieldsCount: customizedFields.length,
        uniqueFields: uniqBy(({ displayName }) => displayName.defaultValue, customizedFields).length,
    });

    // return unique
    return uniqBy(({ displayName }) => displayName.defaultValue, customizedFields).map(value => ({
        key: value.displayName.defaultValue,
        header: value.displayName.defaultValue,
        getCellValue: preparedData => {
            if (preparedData.kind === ApplicationKind.Event) {
                const customizedField = preparedData.customizedFields.find(
                    ({ displayName }) => displayName.defaultValue === value.displayName.defaultValue
                );

                return customizedField ? customizedField.value : '';
            }

            return '';
        },
    }));
};

const getRowSettingsForApplications = async (
    modules: SystemSupportedApplicationModule[],
    moduleTypes: ModuleType[],
    items: PreparedSystemApplicationData[],
    support: SupportedData
) => {
    const { currencyCode, stage, t, timeZone } = support;
    const [, allConsentColumns] = await getSystemConsentSetting(
        support.t,
        modules.map(module => module._type !== ModuleType.SalesOfferModule && module.agreementsModuleId).filter(Boolean)
    );

    const hasPayment = modules.every(module => {
        if (
            module._type === ModuleType.ConfiguratorModule ||
            module._type === ModuleType.StandardApplicationModule ||
            module._type === ModuleType.MobilityModule
        ) {
            return module.scenarios.includes(ApplicationScenario.Payment);
        }

        return false;
    });

    const hasPaymentDetails = !ApplicationStage.Insurance && !ApplicationStage.Appointment;

    const showMobilityDiscountDetails = false;

    const [, allAppointmentColumns] = getSystemAppointmentSetting({ t, timeZone });

    const kycColumns = await getKYCColumns(modules as SystemKycSupportedApplicationModule[], support.t);

    const [, allPaymentColumns] = getSystemPaymentSetting(stage, { t, timeZone }, showMobilityDiscountDetails);
    const [, allInsuranceColumns] = getSystemInsuranceSetting(support.t, currencyCode);
    const isHasCapBPId = items.some(i => i.support.capValues?.businessPartnerId);
    const isHasCapLeadId = items.some(i => i.support.capValues?.leadId);

    return [
        // Application Ids
        ...getMainSettingColumnId(stage, moduleTypes),

        // Appointment details
        ...(stage === ApplicationStage.Appointment ? allAppointmentColumns : []),

        // Application Details
        ...getMainSettingColumns(moduleTypes, support),

        // C@P related values
        ...(stage !== ApplicationStage.Mobility ? getCapColumns(t, isHasCapBPId, isHasCapLeadId) : []),

        // Customer Details
        ...kycColumns,

        // Vehicle Details
        ...getVehicleColumns(moduleTypes, support.hasAppointment, support.t),

        // Payment details
        ...(hasPayment && hasPaymentDetails ? allPaymentColumns : []),

        // Financing details
        ...(stage === ApplicationStage.Financing ? getFinancingColumns(moduleTypes, support) : []),

        // Insurance details
        ...(stage === ApplicationStage.Insurance ? allInsuranceColumns : []),

        // Consents
        ...allConsentColumns,

        // Campaign Values
        ...(stage === ApplicationStage.Appointment || stage === ApplicationStage.Lead
            ? getCampaignValuesColumns(t)
            : []),

        // get customized fields
        ...getCustomizedFieldsColumns(items),
    ].filter(Boolean);
};

const getApplicationModulesSystemRows = async (
    modules: SystemSupportedApplicationModule[],
    items: PreparedSystemApplicationData[],
    support: SupportedData
) => {
    // ensure that the application modules together are on the same group

    // get all application module types
    const moduleTypes = flow([map((module: Module) => module._type), uniq])(modules);

    const settings = await (async () => {
        // ensure we get all applications or mobility
        if (moduleTypes.length === 1 && moduleTypes[0] === ModuleType.MobilityModule) {
            // build for mobility modules
            return getRowSettingForMobility(modules as MobilityModule[], support);
        }
        if (moduleTypes.some(moduleType => moduleType === ModuleType.MobilityModule)) {
            throw new Error('Mobility and Applications should not exist together');
        } else {
            // build the settings based on the module types
            // sdm, bmw, applications
            return getRowSettingsForApplications(modules, moduleTypes, items, support);
        }
    })();

    const header = [[...settings.filter(Boolean).map(setting => setting.header)]];
    const body = items.map(item => settings.filter(Boolean).map(setting => setting.getCellValue(item, support)));

    // DEBUG: Log final column headers for comparison
    console.debug('[Excel] Final column headers:', {
        totalColumns: header[0].length,
        headers: header[0],
        moduleTypes,
        itemsCount: items.length,
        stage: support.stage,
    });

    return [...header, ...body];
};

export default getApplicationModulesSystemRows;
