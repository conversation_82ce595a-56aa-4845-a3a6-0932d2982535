import * as fs from 'fs';
import * as os from 'os';
import * as path from 'path';
import { Document } from 'bson';
import { Job } from 'bull';
import dayjs from 'dayjs';
import { ObjectId } from 'mongodb';
import { nanoid } from 'nanoid';
import XlsxPopulate from 'xlsx-populate';
import { PasswordConfiguration, ApplicationModule, Application } from '../../database';
import getDatabaseContext from '../../database/getDatabaseContext';
import {
    createCompanySMTPTransports,
    sendApplicationExportReady,
    sendApplicationExportPassword,
    sendApplicationExportFail,
} from '../../emails';
import { setHeaderColumnsWidth } from '../../export/exportApplications/shared';
import { Stage } from '../../export/streamExportApplications/enums';
import { getBEApplicationStage } from '../../export/streamExportApplications/shared';
import { PeriodPayload } from '../../export/type';
import { getPassword } from '../../export/utils';
import createLoaders from '../../loaders';
import { createPermissionController, ApplicationPolicyAction } from '../../permissions';
import { getApplicationByCapFormat, getApplicationBySystemFormat } from '../../utils/applicationExportQueries';
import createI18nInstance from '../../utils/createI18nInstance';
import getExcelApplicationRows from '../../utils/excel/applications';
import { FormatCapPurpose } from '../../utils/excel/applications/cap/types';
import { ExcelExportFormat } from '../../utils/excel/enums';
import { uniqueObjectIds } from '../../utils/fp';
import getCompanyEmailContext from '../../utils/getCompanyEmailContext';

const BATCH_SIZE = 500;

export type ProcessApplicationExportMessage = {
    userId: ObjectId;
    moduleIds: string[];
    stage: Stage;
    dealerIds: string[];
    period?: PeriodPayload;
    format: ExcelExportFormat;
    capPurpose?: FormatCapPurpose[];
    nonce?: string;
    languageId?: string;
    filename?: string[];
};

export const processApplicationExport = async (message: ProcessApplicationExportMessage, _job: Job<Document>) => {
    const {
        userId,
        moduleIds: inputModuleIds,
        dealerIds: inputDealerIds,
        stage,
        period,
        format,
        capPurpose,
        nonce: inputNonce,
        languageId,
        filename: inputFilename,
    } = message;

    try {
        const { collections } = await getDatabaseContext();
        const loaders = createLoaders();

        // All validation is now done in streamExportApplications.ts
        // We can assume all inputs are valid at this point
        const user = await collections.users.findOne({ _id: userId });
        const { i18n } = await createI18nInstance();
        await i18n.loadNamespaces(['emails', 'common']);
        const { t } = i18n;

        const beStage = getBEApplicationStage(stage);
        const permissions = await createPermissionController(user);

        const moduleIds = inputModuleIds.map(inputModuleId => new ObjectId(inputModuleId));
        const dealerIds = inputDealerIds.map(dealerId => new ObjectId(dealerId));

        const modules = (await collections.modules.find({ _id: { $in: moduleIds } }).toArray()) as ApplicationModule[];
        const companies = await collections.companies
            .find({ _id: { $in: uniqueObjectIds(modules.map(module => module.companyId)) } })
            .toArray();

        const company = companies?.[0];

        const start = period?.start ? dayjs(period.start).startOf('day').toDate() : null;
        const end = period?.end ? dayjs(period.end).endOf('day').toDate() : null;

        const applicationPermission = await permissions.applications.getFilterQueryForAction(
            ApplicationPolicyAction.View,
            [beStage]
        );

        // Determine which CAP purposes to process
        const purposesToProcess: FormatCapPurpose[] = [];
        if (format === ExcelExportFormat.cap && Array.isArray(capPurpose) && capPurpose.length > 0) {
            purposesToProcess.push(...capPurpose);
        }

        // Use the shared query functions from applicationExportQueries.ts
        const cursor = await (format === ExcelExportFormat.cap
            ? getApplicationByCapFormat({
                  collections,
                  applicationPermission,
                  modules,
                  dealerIds,
                  stage: beStage,
                  start,
                  end,
                  batchSize: BATCH_SIZE,
              })
            : getApplicationBySystemFormat({
                  collections,
                  applicationPermission,
                  modules,
                  dealerIds,
                  stage: beStage,
                  start,
                  end,
                  batchSize: BATCH_SIZE,
              }));

        // Function to process a batch of applications
        const processBatch = async (batch: Application[], currentPurpose?: FormatCapPurpose) => {
            if (batch.length === 0) {
                return null;
            }

            try {
                const formatSettings =
                    format === ExcelExportFormat.cap
                        ? {
                              format,
                              capPurpose: currentPurpose,
                              tenant: `${company.displayName}_${company.countryCode}`.toUpperCase(),
                              stage: beStage, // Use the backend ApplicationStage enum
                              routerFirstLanguage: languageId || null,
                              timeZone: company?.timeZone,
                          }
                        : {
                              format,
                              currencyCode: companies.length > 1 ? undefined : company.currency,
                              timeZone: companies.length > 1 ? undefined : company.timeZone,
                              stage: beStage, // Use the backend ApplicationStage enum
                              routerFirstLanguage: languageId || null,
                          };

                // DEBUG: Log format settings for comparison with processEventApplicationExport.ts
                if (format === ExcelExportFormat.system) {
                    console.debug('[ApplicationExport] Format settings being passed to getExcelApplicationRows:', {
                        format: formatSettings.format,
                        currencyCode: (formatSettings as any).currencyCode,
                        timeZone: formatSettings.timeZone,
                        stage: formatSettings.stage,
                        routerFirstLanguage: formatSettings.routerFirstLanguage,
                        companiesLength: companies.length,
                        companyDetails: {
                            currency: company.currency,
                            timeZone: company.timeZone,
                            displayName: company.displayName,
                        },
                        applicationsCount: batch.length,
                        modulesCount: modules.length,
                    });
                }

                const rows = await getExcelApplicationRows(batch, modules, formatSettings, loaders);

                return rows;
            } catch (error) {
                throw new Error(`Error processing batch: ${error.message}`);
            }
        };

        // Function to fetch the next batch of applications
        const fetchNextBatch = async () => {
            const batch = [];

            // Handle both cursor and array types
            if (Array.isArray(cursor)) {
                // If cursor is an array, we're already done
                return cursor;
            }
            // If cursor is a MongoDB cursor, fetch the next batch
            const fetchApplication = async () => {
                if (batch.length >= BATCH_SIZE) {
                    return;
                }

                const hasNext = await cursor.hasNext();
                if (!hasNext) {
                    return;
                }

                const application = await cursor.next();
                batch.push(application);

                await fetchApplication();
            };

            await fetchApplication();

            return batch;
        };

        // Generate workbooks for each purpose or a single workbook for non-CAP format
        const filePaths = [];
        let exportPassword = null;
        const password = await getPassword(inputNonce);

        // Extract common workbook generation logic to avoid code duplication
        const generateWorkbook = async (currentPurpose?: FormatCapPurpose, index?: number) => {
            const workbook = await XlsxPopulate.fromBlankAsync();
            const worksheet = workbook.sheet(0).name('Default Worksheet');

            let headerAdded = false;
            const allRows = [];

            // Reset cursor position if needed
            if (Array.isArray(cursor)) {
                // If cursor is an array, we need to process it for each purpose
                const rows = await processBatch(cursor, currentPurpose);

                if (rows && Array.isArray(rows)) {
                    allRows.push(...rows);
                }
            } else {
                // If cursor is a MongoDB cursor, we need to clone it for each purpose
                // Since we can't easily clone a cursor, we'll need to re-fetch the data
                const processAllBatches = async () => {
                    const batch = await fetchNextBatch();
                    if (batch.length === 0) {
                        return;
                    }

                    const rows = await processBatch(batch, currentPurpose);

                    if (rows && Array.isArray(rows)) {
                        if (!headerAdded && rows.length > 0) {
                            allRows.push(rows[0]);
                            headerAdded = true;
                        }

                        for (let i = headerAdded ? 1 : 0; i < rows.length; i++) {
                            if (rows[i] && Array.isArray(rows[i])) {
                                allRows.push(rows[i]);
                            }
                        }
                    }

                    await processAllBatches();
                };

                await processAllBatches();
            }

            if (allRows.length > 0) {
                try {
                    const safeRows = allRows.map(row => {
                        if (!row || !Array.isArray(row)) {
                            return [];
                        }

                        return row.map(cell => (cell === null || cell === undefined ? '' : cell));
                    });

                    worksheet.cell('A1').value(safeRows);

                    if (safeRows.length > 0) {
                        setHeaderColumnsWidth(worksheet, safeRows[0]);
                    }
                } catch (error) {
                    throw new Error(`Error adding rows to worksheet: ${error.message || 'Unknown error'}`);
                }
            }

            const filenameIndex = index !== undefined ? index : 0;
            let filename = 'export.xlsx';

            if (inputFilename && inputFilename.length > 0) {
                filename = inputFilename[filenameIndex] || inputFilename[0];

                if (!filename.endsWith('.xlsx')) {
                    filename = `${filename}.xlsx`;
                }
            }

            const nonce = inputNonce ?? nanoid();
            const password = await getPassword(nonce);
            const isPasswordProtected = company.passwordConfiguration !== PasswordConfiguration.Off;

            const buffer = await workbook.outputAsync({ ...(isPasswordProtected && { password }) });

            const tempDir = os.tmpdir();
            const filePath = path.join(tempDir, filename);
            fs.writeFileSync(filePath, buffer);

            return { filename, path: filePath, password: isPasswordProtected ? password : null };
        };

        if (format === ExcelExportFormat.cap && purposesToProcess.length > 0) {
            // For CAP format with multiple purposes, generate a workbook for each purpose
            // Process each purpose sequentially using Promise.all to avoid await inside loops
            const results = await Promise.all(
                purposesToProcess.map(async (purpose, index) => {
                    const result = await generateWorkbook(purpose, index);

                    // Store the password from the first file (they all use the same password)
                    if (index === 0 && result.password) {
                        exportPassword = result.password;
                    }

                    return { filename: result.filename, path: result.path };
                })
            );

            filePaths.push(...results);
        } else {
            // For non-CAP format or single purpose, generate a single workbook
            // If capPurpose is undefined or null, pass undefined to generateWorkbook
            const result = await generateWorkbook(capPurpose && capPurpose.length > 0 ? capPurpose[0] : undefined, 0);

            if (result.password) {
                exportPassword = result.password;
            }

            filePaths.push({ filename: result.filename, path: result.path });
        }

        const emailContext = await getCompanyEmailContext(company);
        const transporter = await createCompanySMTPTransports(company);

        // Get the application type from the translation
        const applicationType = t(`emails:applicationTypes.${stage}`, { defaultValue: 'Applications' });

        // Send the file attachment email with all files
        await sendApplicationExportReady(
            {
                i18n,
                subject: t('emails:applicationExportReady.subject', {
                    companyName: company.displayName,
                    applicationType,
                }),
                data: {
                    user,
                    requestDate: new Date(),
                    emailContext,
                    applicationType,
                },
                to: { name: user.displayName, address: user.email },
                attachments: filePaths.map(file => ({
                    filename: file.filename,
                    path: file.path,
                    contentType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                })),
            },
            transporter,
            emailContext.sender
        );

        // Send the password in a separate email if password protection is enabled
        if (exportPassword) {
            await sendApplicationExportPassword(
                {
                    i18n,
                    subject: t('emails:applicationExportPassword.subject', {
                        companyName: company.displayName,
                        applicationType,
                    }),
                    data: {
                        user,
                        password,
                        emailContext,
                        applicationType,
                    },
                    to: { name: user.displayName, address: user.email },
                },
                transporter,
                emailContext.sender
            );
        }

        // Clean up the temporary files
        for (const file of filePaths) {
            fs.unlinkSync(file.path);
        }
    } catch (error) {
        console.error('Unexpected error in processApplicationExport:', error);

        // Send failure notification email to the user
        try {
            const { collections } = await getDatabaseContext();
            const user = await collections.users.findOne({ _id: userId });

            if (!user) {
                console.error('User not found for sending failure notification');

                return;
            }

            const { i18n } = await createI18nInstance();
            await i18n.loadNamespaces(['emails', 'common']);
            const { t } = i18n;

            const moduleIds = inputModuleIds.map(inputModuleId => new ObjectId(inputModuleId));
            const modules = (await collections.modules
                .find({ _id: { $in: moduleIds } })
                .toArray()) as ApplicationModule[];
            const companies = await collections.companies
                .find({ _id: { $in: uniqueObjectIds(modules.map(module => module.companyId)) } })
                .toArray();

            const company = companies?.[0];

            if (!company) {
                console.error('Company not found for sending failure notification');

                return;
            }

            const emailContext = await getCompanyEmailContext(company);
            const transporter = await createCompanySMTPTransports(company);

            // Get the application type from the translation
            const applicationType = t(`emails:applicationTypes.${stage}`, { defaultValue: 'Applications' });

            await sendApplicationExportFail(
                {
                    i18n,
                    subject: t('emails:applicationExportFail.subject', {
                        companyName: company.displayName,
                        applicationType,
                    }),
                    data: {
                        user,
                        requestDate: new Date(),
                        emailContext,
                        applicationType,
                    },
                    to: { name: user.displayName, address: user.email },
                },
                transporter,
                emailContext.sender
            );
        } catch (emailError) {
            console.error('Error sending application export failure notification:', emailError);
        }
    }
};
