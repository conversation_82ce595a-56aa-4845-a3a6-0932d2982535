import * as fs from 'fs';
import * as os from 'os';
import * as path from 'path';
import type { Document } from 'bson';
import type { Job } from 'bull';
import dayjs from 'dayjs';
import type { TFunction } from 'i18next';
import type { FindCursor } from 'mongodb';
import { ObjectId } from 'mongodb';
import { nanoid } from 'nanoid';
import XlsxPopulate from 'xlsx-populate';
import { ApplicationKind, ApplicationStatus, PasswordConfiguration } from '../../database';
import type { Company, EventApplicationModule, User, Event, ApplicationStage, Application } from '../../database';
import { AuditTrailKind } from '../../database/documents/AuditTrail/core';
import type {
    ApplicationSubmittedToSystemAuditTrail,
    ApplicationResubmittedToSystemAuditTrail,
} from '../../database/documents/AuditTrail/types/applications';
import type { DatabaseContext } from '../../database/getDatabaseContext';
import getDatabaseContext from '../../database/getDatabaseContext';
import {
    createCompanySMTPTransports,
    sendApplicationExportReady,
    sendApplicationExportPassword,
    sendApplicationExportFail,
} from '../../emails';
import { setHeaderColumnsWidth } from '../../export/exportApplications/shared';
import type { PeriodPayload } from '../../export/type';
import { getPassword, getPeriodFilter } from '../../export/utils';
import createLoaders from '../../loaders';
import type { Loaders } from '../../loaders';
import { createPermissionController, ApplicationPolicyAction } from '../../permissions';
import type { PermissionController } from '../../permissions';
import createI18nInstance from '../../utils/createI18nInstance';
import getExcelApplicationRows from '../../utils/excel/applications';
import { ExcelExportFormat } from '../../utils/excel/enums';
import getCompanyEmailContext from '../../utils/getCompanyEmailContext';

const BATCH_SIZE = 500;

export interface ProcessEventApplicationExportMessage {
    userId: ObjectId;
    eventId: string;
    applicationIds: string[];
    dealerIds: string[];
    stage: ApplicationStage;
    period?: PeriodPayload;
    format: ExcelExportFormat.system | ExcelExportFormat.reporting;
    nonce?: string;
    languageId?: string;
    filename?: string[];
}

interface EventExportContext {
    userId: ObjectId;
    eventId: ObjectId;
    event: Event;
    eventModule: EventApplicationModule;
    company: Company;
    user: User;
    loaders: Loaders;
    permissions: PermissionController;
    applicationPermission: Record<string, unknown>;
    dealerIdsInRequest: ObjectId[];
    stage: ApplicationStage;
    period?: PeriodPayload;
    format: ExcelExportFormat.system | ExcelExportFormat.reporting;
    collections: DatabaseContext['collections'];
    i18n: Awaited<ReturnType<typeof createI18nInstance>>['i18n'];
    t: TFunction;
}

type ExportFilePath = {
    filePath: string;
    filename: string;
    password?: string;
};

// Initialize export context
const initializeEventApplicationExportContext = async (
    message: ProcessEventApplicationExportMessage
): Promise<EventExportContext> => {
    const { userId, eventId: inputEventId, dealerIds: queryDealerIds, stage, period, format, languageId } = message;

    const { collections } = await getDatabaseContext();
    const eventId = new ObjectId(inputEventId);

    // Parallel initialization of independent resources
    const [event, user, loaders, { i18n }] = await Promise.all([
        collections.events.findOne({ _id: eventId }),
        collections.users.findOne({ _id: userId }),
        createLoaders(),
        createI18nInstance(languageId || null),
    ]);

    if (!event) {
        throw new Error('Event not found');
    }

    if (!user) {
        throw new Error('User not found');
    }

    // Get event module and company in parallel
    const [eventModule, company] = await Promise.all([
        collections.modules.findOne({ _id: event.moduleId }) as Promise<EventApplicationModule>,
        // We need the event module first to get company, so this will be done after
        event.moduleId
            ? collections.modules.findOne({ _id: event.moduleId }).then(async module => {
                  if (!module) {
                      return null;
                  }

                  return collections.companies.findOne({ _id: (module as EventApplicationModule).companyId });
              })
            : Promise.resolve(null),
    ]);

    if (!eventModule) {
        throw new Error('Event module not found');
    }

    if (!company) {
        throw new Error('Company not found');
    }

    // Initialize i18n and permissions
    await i18n.loadNamespaces(['emails', 'common']);
    const { t } = i18n;

    const permissions = await createPermissionController(user);
    const applicationPermission = await permissions.applications.getFilterQueryForAction(ApplicationPolicyAction.View);

    // Get dealer IDs
    const dealerIdsInRequest = queryDealerIds?.map(dealerId => new ObjectId(dealerId)) ?? [];

    return {
        userId,
        eventId,
        event,
        eventModule,
        company,
        user,
        loaders,
        permissions,
        applicationPermission,
        dealerIdsInRequest,
        stage,
        period,
        format,
        collections,
        i18n,
        t,
    };
};

// Async batch iterator following the pattern from processLeadExport.ts
const createEventApplicationBatchIterator = (cursor: FindCursor<Application>, batchSize: number) => ({
    async *[Symbol.asyncIterator]() {
        let batchCount = 0;

        while (true) {
            const batch: Application[] = [];

            try {
                while (batch.length < batchSize) {
                    // eslint-disable-next-line no-await-in-loop
                    const hasNext = await cursor.hasNext();
                    if (!hasNext) {
                        break;
                    }

                    // eslint-disable-next-line no-await-in-loop
                    const application = await cursor.next();
                    if (application) {
                        batch.push(application);
                    }
                }

                if (batch.length === 0) {
                    break;
                }

                yield { batch, batchIndex: batchCount++ };

                // Memory pressure check - yield control periodically
                if (batchCount % 10 === 0) {
                    // eslint-disable-next-line no-await-in-loop
                    await new Promise(resolve => {
                        setImmediate(resolve);
                    });
                }
            } catch (error) {
                console.error(`Error fetching batch ${batchCount}:`, error);
                throw error;
            }
        }
    },
});

// Generate workbook with streaming
const generateEventApplicationWorkbookStreaming = async (
    cursor: FindCursor<Application>,
    context: EventExportContext,
    languageId?: string
) => {
    const workbook = await XlsxPopulate.fromBlankAsync();
    const worksheet = workbook.sheet(0).name('Export Data');

    let headerAdded = false;
    let currentRow = 1;
    let totalProcessed = 0;

    const batchIterator = createEventApplicationBatchIterator(cursor, BATCH_SIZE);

    // Use for-await-of to properly handle async iterator
    for await (const { batch, batchIndex } of batchIterator) {
        if (batch.length === 0) {
            continue;
        }

        try {
            const rows = await processEventApplications(
                context.loaders,
                batch,
                [context.eventModule],
                context.company,
                context.stage,
                context.format,
                languageId
            );

            if (rows.length === 0) {
                continue;
            }

            // Add header if not added yet (rows is array of arrays: [header, ...dataRows])
            if (!headerAdded && rows.length > 0) {
                const headerRow = rows[0];
                for (let i = 0; i < headerRow.length; i++) {
                    worksheet.cell(currentRow, i + 1).value(headerRow[i]);
                }
                setHeaderColumnsWidth(worksheet, headerRow);
                currentRow++;
                headerAdded = true;
            }

            // Add data rows (skip header row)
            const dataStartIndex = headerAdded ? 1 : 0;
            for (let rowIndex = dataStartIndex; rowIndex < rows.length; rowIndex++) {
                const row = rows[rowIndex];
                for (let i = 0; i < row.length; i++) {
                    worksheet.cell(currentRow, i + 1).value(row[i]);
                }
                currentRow++;
                totalProcessed++;
            }

            if (batchIndex % 20 === 0) {
                console.info(
                    `[EventApplicationExport] Processed ${totalProcessed} applications (${batchIndex + 1} batches)`
                );
            }
        } catch (error) {
            throw new Error(`Error processing batch: ${error.message}`);
        }
    }

    console.info(`[EventApplicationExport] Export completed: ${totalProcessed} total applications processed`);

    return workbook;
};

// Process event applications function
const processEventApplications = async (
    loaders: Loaders,
    applications: Application[],
    modules: EventApplicationModule[],
    company: Company,
    stage: ApplicationStage,
    format: ExcelExportFormat.system | ExcelExportFormat.reporting,
    languageId?: string
) => {
    // No applications to process
    if (applications.length === 0) {
        return [];
    }

    try {
        return getExcelApplicationRows(
            applications,
            modules,
            {
                format,
                currencyCode: company.currency,
                timeZone: company.timeZone,
                stage,
                routerFirstLanguage: languageId || null,
            },
            loaders
        );
    } catch (error) {
        throw new Error(`Error processing event applications batch: ${error.message}`);
    }
};

interface EventApplicationQueryParams {
    collections: DatabaseContext['collections'];
    applicationPermission: Record<string, unknown>;
    eventId: ObjectId;
    dealerIds: ObjectId[];
    stage: ApplicationStage;
    periodFilter: Record<string, unknown>;
}

// System format query for event applications (with audit trail logic) - finds ALL applications for the event
const getEventApplicationsBySystemFormat = async ({
    collections,
    applicationPermission,
    eventId,
    dealerIds,
    periodFilter,
}: EventApplicationQueryParams): Promise<FindCursor<Application>> => {
    // First get ALL application suite IDs for the event (not just pre-selected ones)
    const applicationSuiteIds = await collections.applications
        .find({
            eventId,
            isDraft: false,
            status: { $ne: ApplicationStatus.Drafted },
            dealerId: { $in: dealerIds },
            kind: ApplicationKind.Event,
            '_versioning.isLatest': true,
            ...periodFilter,
        })
        .map((item: Application) => item._versioning.suiteId)
        .toArray();

    // Get submitted audit trail application IDs
    const submittedAuditTrailApplicationIds = await collections.auditTrails
        .find({
            _kind: {
                $in: [AuditTrailKind.ApplicationSubmittedToSystem, AuditTrailKind.ApplicationResubmittedToSystem],
            },
            applicationSuiteId: { $in: applicationSuiteIds },
        })
        .map(
            (auditTrail: ApplicationSubmittedToSystemAuditTrail | ApplicationResubmittedToSystemAuditTrail) =>
                auditTrail.applicationId
        )
        .toArray();

    // Final query: get ALL event applications plus any from audit trails
    const query = {
        $and: [
            applicationPermission,
            {
                eventId,
                isDraft: false,
                status: { $ne: ApplicationStatus.Drafted },
                dealerId: { $in: dealerIds },
                kind: ApplicationKind.Event,
                // Include both original applications and audit trail applications
                ...(submittedAuditTrailApplicationIds.length > 0
                    ? {
                          $or: [{ '_versioning.isLatest': true }, { _id: { $in: submittedAuditTrailApplicationIds } }],
                      }
                    : { '_versioning.isLatest': true }),
                ...periodFilter,
            },
        ],
    };

    return collections.applications.find(query).batchSize(BATCH_SIZE).maxTimeMS(300000);
};

// Generate export files
const generateEventApplicationExportFiles = async (
    context: EventExportContext,
    message: ProcessEventApplicationExportMessage
): Promise<ExportFilePath[]> => {
    const { collections, applicationPermission, eventId, dealerIdsInRequest, period, stage } = context;
    const { filename: inputFilename, nonce: inputNonce } = message;

    let cursor: FindCursor<Application> | null = null;
    const filePaths: ExportFilePath[] = [];

    try {
        // Apply period filtering if provided
        const periodFilter = period
            ? getPeriodFilter(period.start ? new Date(period.start) : null, period.end ? new Date(period.end) : null)
            : {};

        // Use system format query strategy
        cursor = await getEventApplicationsBySystemFormat({
            collections,
            applicationPermission,
            eventId,
            dealerIds: dealerIdsInRequest,
            stage,
            periodFilter,
        });

        // Check if cursor has any data
        const hasData = await cursor.hasNext();

        if (!hasData) {
            console.warn('[EventApplicationExport] No applications found matching criteria');

            // Return empty file paths but don't throw error - let the email system handle it
            return [];
        }

        // Generate workbook
        const workbook = await generateEventApplicationWorkbookStreaming(cursor, context, message.languageId);

        // Determine password protection
        const passwordProtect = context.company.passwordConfiguration !== PasswordConfiguration.Off;
        const password = passwordProtect ? await getPassword(inputNonce || nanoid()) : null;

        // Generate filename
        const filename = inputFilename?.[0] || `event_applications_${dayjs().format('DD_MM_YYYY')}.xlsx`;
        const tempDir = os.tmpdir();
        const filePath = path.join(tempDir, `${nanoid()}_temp_${filename}`);

        // Save workbook
        const buffer = await workbook.outputAsync({ ...(passwordProtect && { password }) });
        await fs.promises.writeFile(filePath, buffer);

        filePaths.push({
            filePath,
            filename,
            password: password || undefined,
        });

        return filePaths;
    } catch (error) {
        console.error('Error generating event application export files:', error);
        throw error;
    } finally {
        if (cursor) {
            await cursor.close();
        }
    }
};

export const processEventApplicationExport = async (
    message: ProcessEventApplicationExportMessage,
    _job: Job<Document>
) => {
    let filePaths: ExportFilePath[] = [];

    try {
        // Initialize export context
        const context = await initializeEventApplicationExportContext(message).catch(err => {
            throw new Error(`Context initialization failed: ${err.message}`);
        });

        // Generate export files
        filePaths = await generateEventApplicationExportFiles(context, message).catch(err => {
            throw new Error(`File generation failed: ${err.message}`);
        });

        // Send emails
        const exportPassword = filePaths[0]?.password;
        const applicationType = 'Events';

        await sendEventApplicationExportEmails(context, filePaths, applicationType, exportPassword).catch(err => {
            throw new Error(`Email sending failed: ${err.message}`);
        });
    } catch (error) {
        // Send failure notification
        await sendEventApplicationFailureNotification(message, error);

        throw error;
    } finally {
        // Clean up temporary files
        if (filePaths.length > 0) {
            await Promise.all(
                filePaths.map(async ({ filePath }) => {
                    try {
                        await fs.promises.unlink(filePath);
                    } catch (unlinkError) {
                        // eslint-disable-next-line no-console
                        console.error('Failed to clean up file', {
                            filePath,
                            error: unlinkError instanceof Error ? unlinkError.message : 'Unknown error',
                        });
                    }
                })
            );
        }
    }
};

// Send export emails
const sendEventApplicationExportEmails = async (
    context: EventExportContext,
    filePaths: ExportFilePath[],
    applicationType: string,
    exportPassword?: string
) => {
    const { company, user, i18n, t } = context;

    const emailContext = await getCompanyEmailContext(company);
    const transports = await createCompanySMTPTransports(company);

    await sendApplicationExportReady(
        {
            i18n,
            subject: t('emails:applicationExportReady.subject', {
                companyName: company.displayName,
                applicationType,
            }),
            data: {
                user,
                requestDate: new Date(),
                emailContext,
                applicationType,
            },
            to: { name: user.displayName, address: user.email },
            attachments: filePaths.map(fp => ({
                filename: fp.filename,
                path: fp.filePath,
                contentType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            })),
        },
        transports,
        emailContext.sender
    );

    // Send password email if needed
    if (exportPassword) {
        await sendApplicationExportPassword(
            {
                i18n,
                subject: t('emails:applicationExportPassword.subject', {
                    companyName: company.displayName,
                    applicationType,
                }),
                data: {
                    user,
                    password: exportPassword,
                    emailContext,
                    applicationType,
                },
                to: { name: user.displayName, address: user.email },
            },
            transports,
            emailContext.sender
        );
    }
};

// Send failure notification
const sendEventApplicationFailureNotification = async (message: ProcessEventApplicationExportMessage, _error: any) => {
    try {
        const { collections } = await getDatabaseContext();
        const user = await collections.users.findOne({ _id: message.userId });

        if (!user) {
            console.error('User not found for sending failure notification');

            return;
        }

        // Get event and company from event module
        const eventId = new ObjectId(message.eventId);
        const event = await collections.events.findOne({ _id: eventId });
        if (!event) {
            console.error('Event not found for sending failure notification');

            return;
        }

        const eventModule = await collections.modules.findOne({ _id: event.moduleId });
        if (!eventModule) {
            console.error('Event module not found for sending failure notification');

            return;
        }

        const company = await collections.companies.findOne({ _id: eventModule.companyId });
        if (!company) {
            console.error('Company not found for sending failure notification');

            return;
        }

        const { i18n } = await createI18nInstance(message.languageId || null);
        await i18n.loadNamespaces(['emails', 'common']);
        const { t } = i18n;

        const emailContext = await getCompanyEmailContext(company);
        const transports = await createCompanySMTPTransports(company);

        await sendApplicationExportFail(
            {
                i18n,
                subject: t('emails:applicationExportFail.subject', {
                    companyName: company.displayName,
                    applicationType: 'Events',
                }),
                data: {
                    user,
                    requestDate: new Date(),
                    emailContext,
                    applicationType: 'Events',
                },
                to: { name: user.displayName, address: user.email },
            },
            transports,
            emailContext.sender
        );
    } catch (emailError) {
        console.error('Error sending failure notification:', emailError);
    }
};
