// Debug logger utility
import * as fs from 'fs';
import * as os from 'os';
import * as path from 'path';
import { Document } from 'bson';
import { Job } from 'bull';
import dayjs from 'dayjs';
import { TFunction } from 'i18next';
import { FindCursor, ObjectId } from 'mongodb';
import { nanoid } from 'nanoid';
import XlsxPopulate from 'xlsx-populate';
import { ApplicationStage as FrontendStage } from '../../../app/utilities/getApplicationFileName';
import {
    ApplicationKind,
    ApplicationStatus,
    Company,
    EventApplication,
    EventApplicationModule,
    PasswordConfiguration,
} from '../../database';
import { AuditTrailKind } from '../../database/documents/AuditTrail/core';
import type {
    ApplicationSubmittedToSystemAuditTrail,
    ApplicationResubmittedToSystemAuditTrail,
} from '../../database/documents/AuditTrail/types/applications';
import getDatabaseContext from '../../database/getDatabaseContext';
import {
    createCompanySMTPTransports,
    sendApplicationExportReady,
    sendApplicationExportPassword,
    sendApplicationExportFail,
} from '../../emails';
import { getBEApplicationStage } from '../../export/exportApplications';
import { setHeaderColumnsWidth } from '../../export/exportApplications/shared';
import { PeriodPayload } from '../../export/type';
import { getPassword, getPeriodFilter } from '../../export/utils';
import createLoaders, { Loaders } from '../../loaders';
import { createPermissionController, ApplicationPolicyAction, PermissionController } from '../../permissions';
import createI18nInstance from '../../utils/createI18nInstance';
import getExcelApplicationRows from '../../utils/excel/applications';
import { ExcelExportFormat } from '../../utils/excel/enums';
import getCompanyEmailContext from '../../utils/getCompanyEmailContext';

const BATCH_SIZE = 500;

// Convert string stage to frontend enum for getBEApplicationStage
const convertStringToFrontendStage = (stage: string): FrontendStage | null => {
    switch (stage) {
        case 'Financing':
            return FrontendStage.Financing;
        case 'Lead':
            return FrontendStage.Lead;
        case 'Reservation':
            return FrontendStage.Reservation;
        case 'Mobility':
            return FrontendStage.Mobility;
        case 'Appointment':
            return FrontendStage.Appointment;
        case 'Insurance':
            return FrontendStage.Insurance;
        case 'VisitAppointment':
            return FrontendStage.VisitAppointment;
        case 'TradeIn':
            return FrontendStage.TradeIn;
        default:
            return null;
    }
};

export type ProcessEventApplicationExportMessage = {
    userId: ObjectId;
    eventId: string;
    applicationIds: string[];
    dealerIds: string[];
    stage: string;
    period?: PeriodPayload;
    format: 'system';
    nonce?: string;
    languageId?: string;
    filename?: string[];
};

type EventExportContext = {
    userId: ObjectId;
    eventId: ObjectId;
    event: any;
    eventModule: EventApplicationModule;
    company: Company;
    user: any;
    loaders: Loaders;
    permissions: PermissionController;
    applicationPermission: any;
    dealerIdsInRequest: ObjectId[];
    stage: any;
    period?: PeriodPayload;
    format: 'system';
    collections: any;
    i18n: any;
    t: TFunction;
};

type ExportFilePath = {
    filePath: string;
    filename: string;
    password?: string;
};

// Initialize export context
const initializeEventApplicationExportContext = async (
    message: ProcessEventApplicationExportMessage
): Promise<EventExportContext> => {
    console.debug('[EventApplicationExport] Starting context initialization', {
        userId: message.userId.toString(),
        eventId: message.eventId,
        stage: message.stage,
    });

    const {
        userId,
        eventId: inputEventId,
        dealerIds: queryDealerIds,
        stage: inputStage,
        period,
        format,
        languageId,
    } = message;

    const { collections } = await getDatabaseContext();
    const eventId = new ObjectId(inputEventId);

    // Get event and validate
    console.debug('[EventApplicationExport] Fetching event');
    const event = await collections.events.findOne({ _id: eventId });
    if (!event) {
        console.error('[EventApplicationExport] Event not found', { eventId: eventId.toString() });
        throw new Error('Event not found');
    }
    console.debug('[EventApplicationExport] Event found', { eventId: event._id.toString() });

    // Get event module
    console.debug('[EventApplicationExport] Fetching event module');
    const eventModule = (await collections.modules.findOne({
        _id: event.moduleId,
    })) as EventApplicationModule;

    if (!eventModule) {
        console.error('[EventApplicationExport] Event module not found', { moduleId: event.moduleId.toString() });
        throw new Error('Event module not found');
    }
    console.debug('[EventApplicationExport] Event module found', { moduleId: eventModule._id.toString() });

    // Get user and company
    console.debug('[EventApplicationExport] Fetching user');
    const user = await collections.users.findOne({ _id: userId });
    if (!user) {
        console.error('[EventApplicationExport] User not found', { userId: userId.toString() });
        throw new Error('User not found');
    }
    console.debug('[EventApplicationExport] User found', { userId: user._id.toString() });

    // Get company from event module
    console.debug('[EventApplicationExport] Fetching company');
    const company = await collections.companies.findOne({ _id: eventModule.companyId });
    if (!company) {
        console.error('[EventApplicationExport] Company not found', { companyId: eventModule.companyId.toString() });
        throw new Error('Company not found');
    }
    console.debug('[EventApplicationExport] Company found', { companyId: company._id.toString() });

    // Create loaders and permissions
    console.debug('[EventApplicationExport] Creating loaders and permissions');
    const loaders = createLoaders();
    const permissions = await createPermissionController(user);
    const applicationPermission = await permissions.applications.getFilterQueryForAction(ApplicationPolicyAction.View);
    console.debug('[EventApplicationExport] Permissions created');

    // Get dealer IDs
    const dealerIdsInRequest = queryDealerIds?.map(dealerId => new ObjectId(dealerId)) ?? [];
    console.debug('[EventApplicationExport] Dealer IDs processed', { dealerCount: dealerIdsInRequest.length });

    // Validate stage
    console.debug('[EventApplicationExport] Validating stage', { inputStage });
    const frontendStage = convertStringToFrontendStage(inputStage);
    if (!frontendStage) {
        console.error('[EventApplicationExport] Invalid stage', { stage: inputStage });
        throw new Error('Invalid stage');
    }
    const stage = getBEApplicationStage(frontendStage);
    console.debug('[EventApplicationExport] Stage validated', { frontendStage, backendStage: stage });

    // Create i18n instance
    console.debug('[EventApplicationExport] Creating i18n instance', { languageId: languageId || 'default' });
    const { i18n } = await createI18nInstance(languageId || null);
    await i18n.loadNamespaces(['emails', 'common']);
    const { t } = i18n;

    console.debug('[EventApplicationExport] Context initialization completed');

    return {
        userId,
        eventId,
        event,
        eventModule,
        company,
        user,
        loaders,
        permissions,
        applicationPermission,
        dealerIdsInRequest,
        stage,
        period,
        format,
        collections,
        i18n,
        t,
    };
};

// Async batch iterator following the pattern from processLeadExport.ts
const createEventApplicationBatchIterator = (cursor: FindCursor<EventApplication>, batchSize: number) => ({
    async *[Symbol.asyncIterator]() {
        let batchCount = 0;

        while (true) {
            const batch: EventApplication[] = [];

            try {
                while (batch.length < batchSize) {
                    // eslint-disable-next-line no-await-in-loop
                    const hasNext = await cursor.hasNext();
                    if (!hasNext) {
                        break;
                    }

                    // eslint-disable-next-line no-await-in-loop
                    const application = await cursor.next();
                    if (application) {
                        batch.push(application);
                    }
                }

                if (batch.length === 0) {
                    break;
                }

                console.debug('[EventApplicationExport] Yielding batch', {
                    batchSize: batch.length,
                    batchIndex: batchCount,
                });
                yield { batch, batchIndex: batchCount++ };

                // Memory pressure check - yield control periodically
                if (batchCount % 10 === 0) {
                    // eslint-disable-next-line no-await-in-loop
                    await new Promise(resolve => {
                        setImmediate(resolve);
                    });
                }
            } catch (error) {
                console.error(`[EventApplicationExport] Error fetching batch ${batchCount}:`, error);
                throw error;
            }
        }
    },
});

// Generate workbook with streaming
const generateEventApplicationWorkbookStreaming = async (
    cursor: FindCursor<EventApplication>,
    context: EventExportContext,
    languageId?: string
) => {
    console.debug('[EventApplicationExport] Starting workbook generation');
    const startTime = Date.now();
    const workbook = await XlsxPopulate.fromBlankAsync();
    const worksheet = workbook.sheet(0).name('Export Data');

    let headerAdded = false;
    let currentRow = 1;
    let totalProcessed = 0;

    const batchIterator = createEventApplicationBatchIterator(cursor, BATCH_SIZE);

    // Use for-await-of to properly handle async iterator
    for await (const { batch, batchIndex } of batchIterator) {
        if (batch.length === 0) {
            continue;
        }

        const batchStartTime = Date.now();
        console.debug(`[EventApplicationExport] Processing batch ${batchIndex + 1}`, {
            batchSize: batch.length,
            totalProcessed,
        });

        try {
            const rows = await processEventApplications(
                context.loaders,
                batch,
                [context.eventModule],
                [context.company], // Pass as array for consistency with regular application export
                context.stage,
                languageId
            );

            if (rows.length === 0) {
                continue;
            }

            // Add header if not added yet (rows is array of arrays: [header, ...dataRows])
            if (!headerAdded && rows.length > 0) {
                const headerRow = rows[0];
                console.debug('[EventApplicationExport] Adding header row', {
                    columnCount: headerRow.length,
                });
                for (let i = 0; i < headerRow.length; i++) {
                    worksheet.cell(currentRow, i + 1).value(headerRow[i]);
                }
                setHeaderColumnsWidth(worksheet, headerRow);
                currentRow++;
                headerAdded = true;
            }

            // Add data rows (skip header row)
            const dataStartIndex = headerAdded ? 1 : 0;
            for (let rowIndex = dataStartIndex; rowIndex < rows.length; rowIndex++) {
                const row = rows[rowIndex];
                for (let i = 0; i < row.length; i++) {
                    worksheet.cell(currentRow, i + 1).value(row[i]);
                }
                currentRow++;
                totalProcessed++;
            }

            console.debug(`[EventApplicationExport] Batch ${batchIndex + 1} processed`, {
                rowsProcessed: rows.length,
                batchTimeMs: Date.now() - batchStartTime,
            });
        } catch (error) {
            console.error('[EventApplicationExport] Error processing batch:', {
                error: error.message,
                stack: error.stack,
                batchNumber: batchIndex + 1,
                batchSize: batch.length,
            });
            throw new Error(`Error processing batch: ${error.message}`);
        }
    }

    console.debug('[EventApplicationExport] Workbook generation completed', {
        totalProcessed,
        totalBatches: 'completed',
        totalTimeMs: Date.now() - startTime,
        eventId: context.eventId.toString(),
    });

    return workbook;
};

// Process event applications function
const processEventApplications = async (
    loaders: Loaders,
    applications: EventApplication[],
    modules: EventApplicationModule[],
    companies: Company[],
    stage: any,
    languageId?: string
) => {
    // No applications to process
    if (applications.length === 0) {
        return [];
    }

    try {
        // Use the same format settings logic as processApplicationExport.ts for consistency
        const company = companies[0]; // Event applications are always from a single company

        // Create format settings object with explicit typing to match processApplicationExport.ts
        const formatSettings = {
            format: ExcelExportFormat.system as ExcelExportFormat.system,
            // Apply the same multi-company logic as regular application export for consistency
            // Even though event applications are single-company, this ensures identical column headers
            currencyCode: companies.length > 1 ? undefined : company.currency,
            timeZone: companies.length > 1 ? undefined : company.timeZone,
            stage,
            routerFirstLanguage: languageId || null,
        };

        // DEBUG: Log format settings for comparison with processApplicationExport.ts
        console.debug('[EventApplicationExport] Format settings being passed to getExcelApplicationRows:', {
            format: formatSettings.format,
            currencyCode: formatSettings.currencyCode,
            timeZone: formatSettings.timeZone,
            stage: formatSettings.stage,
            routerFirstLanguage: formatSettings.routerFirstLanguage,
            companiesLength: companies.length,
            companyDetails: {
                currency: company.currency,
                timeZone: company.timeZone,
                displayName: company.displayName,
            },
            applicationsCount: applications.length,
            modulesCount: modules.length,
        });

        return getExcelApplicationRows(applications, modules, formatSettings, loaders);
    } catch (error) {
        throw new Error(`Error processing event applications batch: ${error.message}`);
    }
};

// Event-specific query functions following exportEventApplication.ts patterns
type EventApplicationQueryParams = {
    collections: any;
    applicationPermission: any;
    eventId: ObjectId;
    dealerIds: ObjectId[];
    stage: any;
    periodFilter: any;
};

// System format query for event applications (with audit trail logic) - finds ALL applications for the event
const getEventApplicationsBySystemFormat = async ({
    collections,
    applicationPermission,
    eventId,
    dealerIds,
    periodFilter,
}: EventApplicationQueryParams): Promise<FindCursor<EventApplication>> => {
    // First get ALL application suite IDs for the event (not just pre-selected ones)
    const applicationSuiteIds = await collections.applications
        .find({
            eventId,
            isDraft: false,
            status: { $ne: ApplicationStatus.Drafted },
            dealerId: { $in: dealerIds },
            kind: ApplicationKind.Event,
            '_versioning.isLatest': true,
            ...periodFilter,
        })
        .map((item: any) => item._versioning.suiteId)
        .toArray();

    console.debug('[EventApplicationExport] Found application suite IDs for ALL event applications', {
        suiteIdsCount: applicationSuiteIds.length,
    });

    // Get submitted audit trail application IDs
    const submittedAuditTrailApplicationIds = await collections.auditTrails
        .find({
            _kind: {
                $in: [AuditTrailKind.ApplicationSubmittedToSystem, AuditTrailKind.ApplicationResubmittedToSystem],
            },
            applicationSuiteId: { $in: applicationSuiteIds },
        })
        .map(
            (auditTrail: ApplicationSubmittedToSystemAuditTrail | ApplicationResubmittedToSystemAuditTrail) =>
                auditTrail.applicationId
        )
        .toArray();

    console.debug('[EventApplicationExport] Found audit trail application IDs', {
        auditTrailIdsCount: submittedAuditTrailApplicationIds.length,
    });

    // Final query: get ALL event applications plus any from audit trails
    const query = {
        $and: [
            applicationPermission,
            {
                eventId,
                isDraft: false,
                status: { $ne: ApplicationStatus.Drafted },
                dealerId: { $in: dealerIds },
                kind: ApplicationKind.Event,
                // Include both original applications and audit trail applications
                ...(submittedAuditTrailApplicationIds.length > 0
                    ? {
                          $or: [{ '_versioning.isLatest': true }, { _id: { $in: submittedAuditTrailApplicationIds } }],
                      }
                    : { '_versioning.isLatest': true }),
                ...periodFilter,
            },
        ],
    };

    console.debug('[EventApplicationExport] System format query - finding ALL event applications', {
        eventId: eventId.toString(),
        auditTrailApplicationIdsCount: submittedAuditTrailApplicationIds.length,
        dealerIdsCount: dealerIds.length,
    });

    return collections.applications.find(query);
};

// Generate export files
const generateEventApplicationExportFiles = async (
    context: EventExportContext,
    message: ProcessEventApplicationExportMessage
): Promise<ExportFilePath[]> => {
    const { collections, applicationPermission, eventId, dealerIdsInRequest, period, format, stage } = context;
    const { filename: inputFilename, nonce: inputNonce } = message;

    let cursor: FindCursor<EventApplication> | null = null;
    const filePaths: ExportFilePath[] = [];

    try {
        // Apply period filtering if provided
        const periodFilter = period
            ? getPeriodFilter(period.start ? new Date(period.start) : null, period.end ? new Date(period.end) : null)
            : {};

        console.debug('[EventApplicationExport] Query parameters - finding ALL event applications', {
            eventId: eventId.toString(),
            dealerIdsCount: dealerIdsInRequest.length,
            format,
            hasPeriodFilter: !!period,
        });

        // Use system format query strategy
        cursor = await getEventApplicationsBySystemFormat({
            collections,
            applicationPermission,
            eventId,
            dealerIds: dealerIdsInRequest,
            stage,
            periodFilter,
        });

        // Check if cursor has any data
        const hasData = await cursor.hasNext();
        console.debug('[EventApplicationExport] Query results', { hasData });

        if (!hasData) {
            console.warn('[EventApplicationExport] No applications found matching criteria');

            // Return empty file paths but don't throw error - let the email system handle it
            return [];
        }

        // Generate workbook
        const workbook = await generateEventApplicationWorkbookStreaming(cursor, context, message.languageId);

        // Determine password protection
        const passwordProtect = context.company.passwordConfiguration !== PasswordConfiguration.Off;
        const password = passwordProtect ? await getPassword(inputNonce || nanoid()) : null;

        // Generate filename
        const filename = inputFilename?.[0] || `event_applications_${dayjs().format('DD_MM_YYYY')}.xlsx`;
        const tempDir = os.tmpdir();
        const filePath = path.join(tempDir, `${nanoid()}_temp_${filename}`);

        // Save workbook
        const buffer = await workbook.outputAsync({ ...(passwordProtect && { password }) });
        await fs.promises.writeFile(filePath, buffer);

        filePaths.push({
            filePath,
            filename,
            password: password || undefined,
        });

        return filePaths;
    } catch (error) {
        console.error('Error generating event application export files:', error);
        throw error;
    } finally {
        if (cursor) {
            await cursor.close();
        }
    }
};

export const processEventApplicationExport = async (
    message: ProcessEventApplicationExportMessage,
    _job: Job<Document>
) => {
    const startTime = Date.now();
    let filePaths: ExportFilePath[] = [];
    let exportError: Error | null = null;

    try {
        // Initialize export context
        const context = await initializeEventApplicationExportContext(message).catch(err => {
            throw new Error(`Context initialization failed: ${err.message}`);
        });

        // Generate export files
        filePaths = await generateEventApplicationExportFiles(context, message).catch(err => {
            throw new Error(`File generation failed: ${err.message}`);
        });

        // Send emails
        const exportPassword = filePaths[0]?.password;
        const applicationType = 'Events';

        await sendEventApplicationExportEmails(context, filePaths, applicationType, exportPassword).catch(err => {
            throw new Error(`Email sending failed: ${err.message}`);
        });

        // Log success metrics
        const totalTime = Date.now() - startTime;
        // eslint-disable-next-line no-console
        console.info('[EventApplicationExport] Export completed successfully', {
            totalTimeMs: totalTime,
            fileCount: filePaths.length,
            userId: message.userId.toString(),
            eventId: message.eventId,
        });
    } catch (error) {
        exportError = error as Error;
        // eslint-disable-next-line no-console
        console.error('[EventApplicationExport] Export failed', {
            error: error instanceof Error ? error.message : 'Unknown error',
            stack: error instanceof Error ? error.stack : undefined,
            userId: message.userId.toString(),
            eventId: message.eventId,
            totalTimeMs: Date.now() - startTime,
        });

        // Send failure notification
        await sendEventApplicationFailureNotification(message, error).catch(notifyError => {
            // eslint-disable-next-line no-console
            console.error('[EventApplicationExport] Failed to send failure notification', {
                error: notifyError instanceof Error ? notifyError.message : 'Unknown error',
            });
        });

        throw error;
    } finally {
        // Clean up temporary files
        if (filePaths.length > 0) {
            await Promise.all(
                filePaths.map(async ({ filePath }) => {
                    try {
                        await fs.promises.unlink(filePath);
                    } catch (unlinkError) {
                        // eslint-disable-next-line no-console
                        console.error('[EventApplicationExport] Failed to clean up file', {
                            filePath,
                            error: unlinkError instanceof Error ? unlinkError.message : 'Unknown error',
                        });
                    }
                })
            );
        }

        // Log final status
        // eslint-disable-next-line no-console
        console.info('[EventApplicationExport] Process completed', {
            status: exportError ? 'failed' : 'success',
            totalTimeMs: Date.now() - startTime,
        });
    }
};

// Send export emails
const sendEventApplicationExportEmails = async (
    context: EventExportContext,
    filePaths: ExportFilePath[],
    applicationType: string,
    exportPassword?: string
) => {
    const { company, user, i18n, t } = context;
    const startTime = Date.now();

    console.debug('[EventApplicationExport] Preparing to send emails', {
        userId: user._id.toString(),
        companyId: company._id.toString(),
        fileCount: filePaths.length,
        hasPassword: !!exportPassword,
    });

    try {
        const emailContext = await getCompanyEmailContext(company);
        const transports = await createCompanySMTPTransports(company);

        console.debug('[EventApplicationExport] Sending export ready email');
        await sendApplicationExportReady(
            {
                i18n,
                subject: t('emails:applicationExportReady.subject', {
                    companyName: company.displayName,
                    applicationType,
                }),
                data: {
                    user,
                    requestDate: new Date(),
                    emailContext,
                    applicationType,
                },
                to: { name: user.displayName, address: user.email },
                attachments: filePaths.map(fp => ({
                    filename: fp.filename,
                    path: fp.filePath,
                    contentType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                })),
            },
            transports,
            emailContext.sender
        );

        // Send password email if needed
        if (exportPassword) {
            console.debug('[EventApplicationExport] Sending password email');
            await sendApplicationExportPassword(
                {
                    i18n,
                    subject: t('emails:applicationExportPassword.subject', {
                        companyName: company.displayName,
                        applicationType,
                    }),
                    data: {
                        user,
                        password: exportPassword,
                        emailContext,
                        applicationType,
                    },
                    to: { name: user.displayName, address: user.email },
                },
                transports,
                emailContext.sender
            );
        }
        console.debug('[EventApplicationExport] Emails sent successfully', {
            totalTimeMs: Date.now() - startTime,
        });
    } catch (error) {
        console.error('[EventApplicationExport] Error sending emails:', {
            error: error.message,
            stack: error.stack,
            userId: user._id.toString(),
            companyId: company._id.toString(),
        });
        throw error;
    }
};

// Send failure notification
const sendEventApplicationFailureNotification = async (message: ProcessEventApplicationExportMessage, _error: any) => {
    try {
        const { collections } = await getDatabaseContext();
        const user = await collections.users.findOne({ _id: message.userId });

        if (!user) {
            console.error('User not found for sending failure notification');

            return;
        }

        // Get event and company from event module
        const eventId = new ObjectId(message.eventId);
        const event = await collections.events.findOne({ _id: eventId });
        if (!event) {
            console.error('Event not found for sending failure notification');

            return;
        }

        const eventModule = await collections.modules.findOne({ _id: event.moduleId });
        if (!eventModule) {
            console.error('Event module not found for sending failure notification');

            return;
        }

        const company = await collections.companies.findOne({ _id: eventModule.companyId });
        if (!company) {
            console.error('Company not found for sending failure notification');

            return;
        }

        const { i18n } = await createI18nInstance(message.languageId || null);
        await i18n.loadNamespaces(['emails', 'common']);
        const { t } = i18n;

        const emailContext = await getCompanyEmailContext(company);
        const transports = await createCompanySMTPTransports(company);

        await sendApplicationExportFail(
            {
                i18n,
                subject: t('emails:applicationExportFail.subject', {
                    companyName: company.displayName,
                    applicationType: 'Events',
                }),
                data: {
                    user,
                    requestDate: new Date(),
                    emailContext,
                    applicationType: 'Events',
                },
                to: { name: user.displayName, address: user.email },
            },
            transports,
            emailContext.sender
        );
    } catch (emailError) {
        console.error('Error sending failure notification:', emailError);
    }
};
